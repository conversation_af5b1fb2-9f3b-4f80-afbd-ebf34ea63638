// 記事メタデータの型定義

export interface Company {
  name: string;
  industry: string;
  logo: string;
}

export interface ArticleMetadata {
  // 基本情報
  title: string;
  slug: string; // URLスラグ（ファイル名から生成）
  
  // 日時情報
  date: string; // YYYY-MM-DD形式
  
  // 分類
  category: string;
  
  // 内容情報
  author: string; // 著者
  readTime: string; // 読了時間
  
  // 表示制御
  published: boolean;
  featured?: boolean; // 注目記事として表示するか
  
  // メディア
  thumbnail: string; // サムネイル画像パス
  
  // 事例情報（オプション）
  company?: Company;
  challenge?: string; // 課題
  result?: string; // 成果
  
  // SEO
  metaDescription?: string; // メタディスクリプション（excerptと異なる場合）
  keywords?: string[]; // キーワード
}

export interface Article {
  metadata: ArticleMetadata;
  content: string; // MDXコンテンツ
  compiledSource?: string; // コンパイル済みMDX（動的インポート用）
}

export interface ArticleListItem {
  metadata: ArticleMetadata;
  // contentは含まない（一覧表示用）
}

// フィルタリング・ソート用の型
export type SortOrder = 'desc' | 'asc';
export type SortBy = 'date' | 'title';

export interface ArticleFilters {
  category?: string;
  published?: boolean;
  featured?: boolean;
}

export interface ArticleQuery {
  filters?: ArticleFilters;
  sortBy?: SortBy;
  sortOrder?: SortOrder;
  limit?: number;
  offset?: number;
}

// ページネーション用
export interface PaginatedArticles {
  articles: ArticleListItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 関連記事用
export interface RelatedArticlesQuery {
  currentSlug: string;
  limit?: number;
  basedOn?: 'category';
}

// 統計情報用
export interface ArticleStats {
  totalArticles: number;
  publishedArticles: number;
  categories: { name: string; count: number }[];
  averageReadTime: number;
}