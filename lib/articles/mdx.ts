import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { 
  ArticleMetadata, 
  Article, 
  ArticleListItem, 
  ArticleQuery, 
  PaginatedArticles,
  RelatedArticlesQuery,
  ArticleStats 
} from './types';

// 記事ディレクトリのパス
const ARTICLES_DIR = path.join(process.cwd(), 'content', 'articles');

/**
 * すべてのMDXファイルのファイル名を取得
 */
function getArticleFileNames(): string[] {
  try {
    if (!fs.existsSync(ARTICLES_DIR)) {
      console.warn(`Articles directory not found: ${ARTICLES_DIR}`);
      return [];
    }
    
    return fs.readdirSync(ARTICLES_DIR)
      .filter(fileName => fileName.endsWith('.mdx'))
      .sort(); // ファイル名でソート
  } catch (error) {
    console.error('Error reading articles directory:', error);
    return [];
  }
}

/**
 * ファイル名からスラグを生成
 */
function getSlugFromFileName(fileName: string): string {
  return fileName.replace(/\.mdx$/, '');
}

/**
 * 単一のMDXファイルを読み取り、パース
 */
async function readArticleFile(fileName: string): Promise<Article | null> {
  try {
    const filePath = path.join(ARTICLES_DIR, fileName);
    
    if (!fs.existsSync(filePath)) {
      console.warn(`Article file not found: ${filePath}`);
      return null;
    }

    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const { data: frontmatter, content } = matter(fileContent);

    // スラグを生成
    const slug = getSlugFromFileName(fileName);

    // フロントマターを型安全にマッピング
    const metadata: ArticleMetadata = {
      title: frontmatter.title || 'Untitled',
      slug,
      date: frontmatter.date || new Date().toISOString().split('T')[0],
      category: frontmatter.category || 'general',
      author: frontmatter.author || 'スペースカウボーイ編集部',
      readTime: frontmatter.readTime || '5分',
      published: frontmatter.published ?? true,
      featured: frontmatter.featured ?? false,
      thumbnail: frontmatter.thumbnail || '/img/article_default.jpg',
      company: frontmatter.company,
      challenge: frontmatter.challenge,
      result: frontmatter.result,
      metaDescription: frontmatter.metaDescription || '',
      keywords: frontmatter.keywords || frontmatter.tags,
    };

    return {
      metadata,
      content
    };
  } catch (error) {
    console.error(`Error reading article ${fileName}:`, error);
    return null;
  }
}

/**
 * スラグから記事を取得
 */
export async function getArticleBySlug(slug: string): Promise<Article | null> {
  const fileName = `${slug}.mdx`;
  return await readArticleFile(fileName);
}

/**
 * すべての記事のメタデータを取得（一覧表示用）
 */
export async function getAllArticles(query: ArticleQuery = {}): Promise<ArticleListItem[]> {
  const fileNames = getArticleFileNames();
  const articles: ArticleListItem[] = [];

  for (const fileName of fileNames) {
    const article = await readArticleFile(fileName);
    
    if (article) {
      // フィルタリング
      if (query.filters) {
        const { category, published, featured } = query.filters;
        
        if (published !== undefined && article.metadata.published !== published) continue;
        if (featured !== undefined && article.metadata.featured !== featured) continue;
        if (category && article.metadata.category !== category) continue;
      }

      articles.push({
        metadata: article.metadata
      });
    }
  }

  // ソート
  const sortBy = query.sortBy || 'date';
  const sortOrder = query.sortOrder || 'desc';
  
  articles.sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'date':
        comparison = new Date(a.metadata.date).getTime() - new Date(b.metadata.date).getTime();
        break;
      case 'title':
        comparison = a.metadata.title.localeCompare(b.metadata.title);
        break;
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });

  // ページネーション
  if (query.limit) {
    const offset = query.offset || 0;
    return articles.slice(offset, offset + query.limit);
  }

  return articles;
}

/**
 * ページネーション付きで記事を取得
 */
export async function getPaginatedArticles(
  page: number = 1, 
  limit: number = 10, 
  query: ArticleQuery = {}
): Promise<PaginatedArticles> {
  const allArticles = await getAllArticles(query);
  const total = allArticles.length;
  const totalPages = Math.ceil(total / limit);
  const offset = (page - 1) * limit;
  
  const paginatedQuery = { ...query, limit, offset };
  const articles = await getAllArticles(paginatedQuery);

  return {
    articles,
    total,
    page,
    limit,
    totalPages
  };
}

/**
 * カテゴリー一覧を取得
 */
export async function getAllCategories(): Promise<string[]> {
  const articles = await getAllArticles({ filters: { published: true } });
  const categories = Array.from(new Set(articles.map(article => article.metadata.category)));
  return categories.sort();
}


/**
 * 関連記事を取得
 */
export async function getRelatedArticles(query: RelatedArticlesQuery): Promise<ArticleListItem[]> {
  const { currentSlug, limit = 3 } = query;
  
  const currentArticle = await getArticleBySlug(currentSlug);
  if (!currentArticle) return [];

  const allArticles = await getAllArticles({ filters: { published: true } });
  const otherArticles = allArticles.filter(article => article.metadata.slug !== currentSlug);

  // 関連度スコアを計算
  const scoredArticles = otherArticles.map(article => {
    let score = 0;
    
    // カテゴリが同じ場合は高スコア
    if (article.metadata.category === currentArticle.metadata.category) {
      score += 3;
    }
    
    return { article, score };
  });

  // スコアでソートして上位を返す
  return scoredArticles
    .filter(item => item.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(item => item.article);
}

/**
 * 記事統計情報を取得
 */
export async function getArticleStats(): Promise<ArticleStats> {
  const allArticles = await getAllArticles();
  const publishedArticles = allArticles.filter(article => article.metadata.published);

  // カテゴリ統計
  const categoryMap = new Map<string, number>();
  publishedArticles.forEach(article => {
    const category = article.metadata.category;
    categoryMap.set(category, (categoryMap.get(category) || 0) + 1);
  });

  const averageReadTime = 0; // 読了時間機能を削除

  return {
    totalArticles: allArticles.length,
    publishedArticles: publishedArticles.length,
    categories: Array.from(categoryMap.entries()).map(([name, count]) => ({ name, count })),
    averageReadTime
  };
}

/**
 * 全記事のスラグ一覧を取得（静的生成用）
 */
export async function getAllArticleSlugs(): Promise<string[]> {
  const articles = await getAllArticles({ filters: { published: true } });
  return articles.map(article => article.metadata.slug);
}