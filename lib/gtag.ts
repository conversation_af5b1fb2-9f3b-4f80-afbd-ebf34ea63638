// Google Analytics 追跡機能

export const GA_TRACKING_ID = 'G-HQFF46WQHF'

// https://developers.google.com/analytics/devguides/collection/gtagjs/pages
export const pageview = (url: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_TRACKING_ID, {
      page_path: url,
    })
  }
}

// https://developers.google.com/analytics/devguides/collection/gtagjs/events
export const event = (action: string, parameters?: Record<string, unknown>) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, parameters)
  }
}

// ツール使用開始
export const trackToolStart = (toolName: string) => {
  event('tool_start', {
    tool_name: toolName,
    event_category: 'tool_usage',
  })
}

// ツール処理完了
export const trackToolComplete = (toolName: string, processingTime?: number) => {
  event('tool_complete', {
    tool_name: toolName,
    processing_time: processingTime,
    event_category: 'tool_usage',
  })
}

// ファイルダウンロード
export const trackDownload = (toolName: string, fileType: string, fileSize?: number) => {
  event('file_download', {
    tool_name: toolName,
    file_type: fileType,
    file_size: fileSize,
    event_category: 'downloads',
  })
}

// ツール間連携
export const trackToolIntegration = (fromTool: string, toTool: string) => {
  event('tool_integration', {
    from_tool: fromTool,
    to_tool: toTool,
    event_category: 'tool_integration',
  })
}

// エラー追跡
export const trackError = (toolName: string, errorType: string, errorMessage?: string) => {
  event('tool_error', {
    tool_name: toolName,
    error_type: errorType,
    error_message: errorMessage,
    event_category: 'errors',
  })
}

// グローバル型定義の拡張
declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event',
      targetId: string,
      config?: Record<string, unknown>
    ) => void
  }
}