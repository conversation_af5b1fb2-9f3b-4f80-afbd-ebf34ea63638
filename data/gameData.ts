// ゲーム・ツール共通のデータ型
export interface GameData {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  tags: string[];
  image: string;
  status: string;
  features: string[];
  link: {
    url: string;
    text: string;
    external: boolean;
  };
}

// ゲームデータ
export const gamesData: GameData[] = [
  {
    id: 'pegasus-fortune',
    title: 'ペガサスの羽みくじ',
    subtitle: '羽を1枚選んで運勢をチェック！',
    description: '5枚の羽から1つを選んで運勢を占う神秘的なおみくじ。',
    tags: ['占い', 'エンターテイメント', '宇宙'],
    image: 'company_image.webp',
    status: '公開',
    features: ['3Dカードアニメーション', '5種類の運勢', '宇宙テーマ'],
    link: {
      url: '/games/pegasus-wing-fortune',
      text: 'ゲームをプレイ',
      external: false
    }
  },
  {
    id: 'esupport-survey',
    title: '周年イベントアンケート',
    subtitle: 'あなたのミッションは？',
    description: '企業イベント向けインタラクティブな体験型アンケート。',
    tags: ['アンケート', 'インタラクティブ', 'ビジネス'],
    image: 'service.webp',
    status: '限定公開',
    features: ['カード選択式UI', '1問集中型'],
    link: {
      url: '/games/eSupport',
      text: 'アンケートに参加',
      external: false
    }
  },
  {
    id: 'tvcha',
    title: 'スタンプで参加ゲーム',
    subtitle: 'LINEのようなスタンプを送ってイベントを盛り上げよう！',
    description: 'スタンプでイベント参加ができるインタラクション。',
    tags: ['イベント', 'テレビ', 'スタンプ', 'アンケート', 'ゲーム'],
    image: 'tvcha.webp',
    status: '限定公開',
    features: ['イベント', 'テレビ', 'スタンプ', 'アンケート', 'ゲーム'],
    link: {
      url: 'https://katsuki1128.github.io/gga/',
      text: 'ゲームをプレイ',
      external: true
    }
  }
];

// クリエイティブ支援ツールデータ
export const toolsData: GameData[] = [
  {
    id: 'jpeg-optimizer',
    title: 'JPEG最適化ツール',
    subtitle: '画像を50KB以下に自動圧縮',
    description: 'Web用に画像を50KB以下に自動調整するツール。',
    tags: ['ツール', '画像処理', 'クリエイティブ'],
    image: 'utility_01.jpg',
    status: '公開',
    features: ['自動リサイズ（最大1000px）', '50KB以下に圧縮', 'ブラウザ完結'],
    link: {
      url: '/games/jpeg-optimizer',
      text: 'ツールを使う',
      external: false
    }
  },
  {
    id: 'image-resizer',
    title: '画像リサイズツール',
    subtitle: '縦横比を保ったまま画像サイズを調整',
    description: 'スライダーで簡単に画像サイズを変更するツール',
    tags: ['ツール', '画像処理', 'リサイズ'],
    image: 'utility_02.jpg',
    status: '公開',
    features: ['縦横比維持', 'スライダー操作', '即座にプレビュー'],
    link: {
      url: '/games/image-resizer',
      text: 'ツールを使う',
      external: false
    }
  },
  {
    id: 'square-cropper',
    title: '正方形クロップツール',
    subtitle: '縦長・横長の画像を正方形にクロップ',
    description: 'SNS投稿などに最適な正方形画像を作成。',
    tags: ['ツール', '画像処理', 'クロップ'],
    image: 'utility_03.jpg',
    status: '公開',
    features: ['中央クロップ', '正方形変換', 'SNS最適化'],
    link: {
      url: '/games/square-cropper',
      text: 'ツールを使う',
      external: false
    }
  },
  {
    id: 'webp-optimizer',
    title: 'WebP変換・最適化ツール',
    subtitle: '次世代画像フォーマットで50KB以下に自動変換',
    description: 'JPEG/PNG画像をWebP形式に変換。Web表示速度を大幅改善。',
    tags: ['ツール', '画像処理', 'WebP', 'Web最適化'],
    image: 'utility_05.jpg',
    status: '公開',
    features: ['次世代フォーマット', '50KB以下変換', 'Web高速化'],
    link: {
      url: '/games/webp-optimizer',
      text: 'ツールを使う',
      external: false
    }
  },
  {
    id: 'text-diff',
    title: 'テキスト比較ツール',
    subtitle: '2つのテキストの差分をハイライト表示',
    description: '文章の変更点を素早く把握。追加部分は緑、削除部分は赤で表示。',
    tags: ['ツール', 'テキスト', '比較'],
    image: 'utility_04.jpg',
    status: '公開',
    features: ['差分ハイライト', '日本語対応', 'ブラウザ完結'],
    link: {
      url: '/games/text-diff',
      text: 'ツールを使う',
      external: false
    }
  }
];