'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { getImageFromURL, base64ToBlob, blobToFile, generateToolURL, getAvailableImageTools } from '../utils/imageUtils';
import { trackToolStart, trackToolComplete, trackDownload, trackToolIntegration, trackError } from '../lib/gtag';

interface ResizedResult {
  blob: Blob;
  size: number;
  dimensions: {
    width: number;
    height: number;
  };
  scale: number;
}

export default function ImageResizer() {
  const [dragActive, setDragActive] = useState(false);
  const [originalFile, setOriginalFile] = useState<File | null>(null);
  const [originalPreview, setOriginalPreview] = useState<string>('');
  const [originalDimensions, setOriginalDimensions] = useState<{width: number, height: number} | null>(null);
  const [resizedResult, setResizedResult] = useState<ResizedResult | null>(null);
  const [resizedPreview, setResizedPreview] = useState<string>('');
  const [scale, setScale] = useState(100);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string>('');
  const [sourceToolInfo, setSourceToolInfo] = useState<{tool: string, message: string} | null>(null);
  const [isGeneratingURL, setIsGeneratingURL] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const availableTools = getAvailableImageTools('image-resizer');

  // URLパラメータから画像を自動読み込み
  useEffect(() => {
    const loadImageFromURL = async () => {
      const imageData = getImageFromURL();
      if (imageData) {
        try {
          const blob = base64ToBlob(imageData.data, 'image/png');
          const file = blobToFile(blob, 'cropped-image.png');
          
          // ソース情報を設定
          if (imageData.source === 'square-cropper') {
            setSourceToolInfo({
              tool: '正方形クロップツール',
              message: '正方形にクロップされた画像を受け取りました'
            });
          } else if (imageData.source === 'jpeg-optimizer') {
            setSourceToolInfo({
              tool: 'JPEG最適化ツール',
              message: '最適化された画像を受け取りました'
            });
          } else if (imageData.source === 'webp-optimizer') {
            setSourceToolInfo({
              tool: 'WebP変換ツール',
              message: 'WebP変換された画像を受け取りました'
            });
          }
          
          // 自動的にファイルを処理（直接呼び出し）
          if (!validateFile(file)) return;

          setError('');
          setIsProcessing(true);
          
          try {
            // プレビュー作成と元画像のサイズ取得
            const originalPreviewUrl = URL.createObjectURL(file);
            setOriginalFile(file);
            setOriginalPreview(originalPreviewUrl);

            // 元画像のサイズを取得
            const img = new Image();
            img.onload = () => {
              setOriginalDimensions({ width: img.width, height: img.height });
              setIsProcessing(false);
            };
            img.src = originalPreviewUrl;

          } catch (err) {
            setError(err instanceof Error ? err.message : '処理中にエラーが発生しました');
            setIsProcessing(false);
          }
          
          // URLのクエリパラメータをクリア（履歴に残さないため）
          if (window.history.replaceState) {
            window.history.replaceState({}, document.title, window.location.pathname);
          }
        } catch (error) {
          setError('前のツールからの画像データの読み込みに失敗しました。');
          console.error('Failed to load image from URL:', error);
        }
      }
    };

    loadImageFromURL();
  }, []);

  // ファイルの検証
  const validateFile = (file: File): boolean => {
    if (!file.type.startsWith('image/')) {
      setError('画像ファイルのみ対応しています。');
      return false;
    }
    if (file.size > 20 * 1024 * 1024) { // 20MB制限
      setError('ファイルサイズが大きすぎます（20MB以下にしてください）。');
      return false;
    }
    return true;
  };

  // 画像のリサイズ
  const resizeImage = useCallback(async (file: File, scalePercent: number): Promise<ResizedResult> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('Canvas context not available'));
        return;
      }

      img.onload = () => {
        const scaleRatio = scalePercent / 100;
        const newWidth = Math.round(img.width * scaleRatio);
        const newHeight = Math.round(img.height * scaleRatio);

        canvas.width = newWidth;
        canvas.height = newHeight;

        // 高品質でリサイズ
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        ctx.drawImage(img, 0, 0, newWidth, newHeight);

        // 元のファイル形式を保持
        const outputFormat = file.type === 'image/png' ? 'image/png' : 'image/jpeg';
        const quality = file.type === 'image/jpeg' ? 0.92 : undefined;

        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Failed to create blob'));
              return;
            }

            resolve({
              blob,
              size: blob.size,
              dimensions: { width: newWidth, height: newHeight },
              scale: scalePercent
            });
          },
          outputFormat,
          quality
        );
      };

      img.onerror = () => {
        reject(new Error('画像の読み込みに失敗しました'));
      };

      img.src = URL.createObjectURL(file);
    });
  }, []);

  // ファイル処理
  const processFile = useCallback(async (file: File) => {
    if (!validateFile(file)) return;

    setError('');
    setIsProcessing(true);
    const startTime = Date.now();
    
    // アナリティクス: ツール使用開始
    trackToolStart('image-resizer');
    
    try {
      // プレビュー作成と元画像のサイズ取得
      const originalPreviewUrl = URL.createObjectURL(file);
      setOriginalFile(file);
      setOriginalPreview(originalPreviewUrl);

      // 元画像のサイズを取得
      const img = new Image();
      img.onload = () => {
        setOriginalDimensions({ width: img.width, height: img.height });
        setIsProcessing(false);
        
        // アナリティクス: 処理完了
        const processingTime = Date.now() - startTime;
        trackToolComplete('image-resizer', processingTime);
      };
      img.src = originalPreviewUrl;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '処理中にエラーが発生しました';
      setError(errorMessage);
      setIsProcessing(false);
      
      // アナリティクス: エラー追跡
      trackError('image-resizer', 'processing_error', errorMessage);
    }
  }, []);

  // スケール変更時の処理
  const handleScaleChange = useCallback(async (newScale: number) => {
    if (!originalFile) return;

    setScale(newScale);
    setIsProcessing(true);

    try {
      const result = await resizeImage(originalFile, newScale);
      const resizedPreviewUrl = URL.createObjectURL(result.blob);
      
      setResizedResult(result);
      setResizedPreview(resizedPreviewUrl);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'リサイズ中にエラーが発生しました');
    } finally {
      setIsProcessing(false);
    }
  }, [originalFile, resizeImage]);

  // ドラッグ&ドロップ処理
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      processFile(e.dataTransfer.files[0]);
    }
  }, [processFile]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  // ファイル選択処理
  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      processFile(e.target.files[0]);
    }
  }, [processFile]);

  // ダウンロード処理
  const handleDownload = useCallback(() => {
    if (!resizedResult || !originalFile) return;

    const link = document.createElement('a');
    link.href = URL.createObjectURL(resizedResult.blob);
    link.download = `resized_${resizedResult.scale}%_${originalFile.name}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // アナリティクス: ダウンロード追跡
    const fileType = originalFile.type || 'unknown';
    trackDownload('image-resizer', fileType, resizedResult.size);
  }, [resizedResult, originalFile]);

  // 他のツールで続行
  const handleContinueWithTool = useCallback(async (toolId: string) => {
    if (!resizedResult) return;

    setIsGeneratingURL(true);
    try {
      // アナリティクス: ツール間連携追跡
      trackToolIntegration('image-resizer', toolId);
      
      const url = await generateToolURL(toolId, resizedResult.blob, 'image-resizer');
      window.location.href = url;
    } catch (error) {
      const errorMessage = 'ツール連携でエラーが発生しました。';
      setError(errorMessage);
      console.error('Tool integration error:', error);
      
      // アナリティクス: エラー追跡
      trackError('image-resizer', 'tool_integration_error', errorMessage);
    } finally {
      setIsGeneratingURL(false);
    }
  }, [resizedResult]);

  // リセット処理
  const handleReset = useCallback(() => {
    setOriginalFile(null);
    setOriginalPreview('');
    setOriginalDimensions(null);
    setResizedResult(null);
    setResizedPreview('');
    setScale(100);
    setError('');
    setSourceToolInfo(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  return (
    <div className="w-full mx-auto">
      {/* アップロードエリア */}
      <div
        className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${
          dragActive
            ? 'border-blue-400 bg-blue-400/10'
            : 'border-gray-600 hover:border-gray-500'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <div className="text-6xl mb-4">🖼️</div>
        <h3 className="text-xl font-bold text-white mb-2">
          画像ファイルをドロップまたはクリック
        </h3>
        <p className="text-gray-400 text-sm mb-4">
          縦横比を保ったままサイズを調整します
        </p>
        <p className="text-gray-500 text-xs">
          対応形式: JPEG, PNG, GIF, WebP（最大20MB）
        </p>
      </div>

      {/* ソースツール情報表示 */}
      {sourceToolInfo && (
        <div className="mt-6 p-4 bg-green-900/50 border border-green-600 rounded-lg">
          <div className="text-center">
            <div className="text-2xl mb-2">✅</div>
            <p className="text-green-300 font-medium">{sourceToolInfo.tool}から画像を受け取りました</p>
            <p className="text-green-400 text-sm mt-1">{sourceToolInfo.message}</p>
          </div>
        </div>
      )}

      {/* エラー表示 */}
      {error && (
        <div className="mt-6 p-4 bg-red-900/50 border border-red-600 rounded-lg">
          <p className="text-red-300 text-sm text-center">{error}</p>
        </div>
      )}

      {/* 画像選択後のコントロール */}
      {originalFile && originalDimensions && (
        <div className="mt-8 space-y-6">
          {/* スケールコントロール */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-bold text-white mb-4 text-center">
              サイズ調整
            </h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <span className="text-gray-300 text-sm w-12">10%</span>
                <input
                  type="range"
                  min="10"
                  max="200"
                  step="5"
                  value={scale}
                  onChange={(e) => handleScaleChange(Number(e.target.value))}
                  className="flex-1 h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
                />
                <span className="text-gray-300 text-sm w-12">200%</span>
              </div>
              <div className="text-center">
                <span className="text-2xl font-bold text-white">{scale}%</span>
                {originalDimensions && (
                  <div className="text-sm text-gray-400 mt-2">
                    {Math.round(originalDimensions.width * scale / 100)} × {Math.round(originalDimensions.height * scale / 100)} px
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* プレビューエリア */}
          {resizedResult && (
            <div className="grid md:grid-cols-2 gap-6">
              {/* Before */}
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-bold text-white mb-4 text-center">
                  変更前
                </h3>
                <div className="aspect-video bg-gray-700 rounded-lg mb-4 overflow-hidden">
                  <img
                    src={originalPreview}
                    alt="Original"
                    className="w-full h-full object-contain"
                  />
                </div>
                <div className="text-center text-sm text-gray-400">
                  <p>サイズ: {(originalFile.size / 1024).toFixed(1)}KB</p>
                  <p>解像度: {originalDimensions.width} × {originalDimensions.height}px</p>
                </div>
              </div>

              {/* After */}
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-bold text-white mb-4 text-center">
                  変更後 ({scale}%)
                </h3>
                <div className="aspect-video bg-gray-700 rounded-lg mb-4 overflow-hidden">
                  <img
                    src={resizedPreview}
                    alt="Resized"
                    className="w-full h-full object-contain"
                  />
                </div>
                <div className="text-center text-sm text-gray-400">
                  <p>サイズ: {(resizedResult.size / 1024).toFixed(1)}KB</p>
                  <p>
                    解像度: {resizedResult.dimensions.width} × {resizedResult.dimensions.height}px
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* アクションボタン */}
          <div className="flex flex-col gap-4">
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              {resizedResult && (
                <button
                  onClick={handleDownload}
                  disabled={isProcessing || isGeneratingURL}
                  className="hero-cta-button inline-flex items-center disabled:opacity-50"
                >
                  <span className="text-xl mr-2">💾</span>
                  リサイズ画像をダウンロード
                </button>
              )}
              <button
                onClick={handleReset}
                disabled={isGeneratingURL}
                className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors disabled:opacity-50"
              >
                別の画像を選択
              </button>
            </div>
            
            {/* 他のツールとの連携ボタン */}
            {resizedResult && availableTools.length > 0 && (
              <div className="border-t border-gray-600 pt-4">
                <p className="text-center text-gray-400 text-sm mb-3">他の画像処理ツールで続行</p>
                <div className="flex flex-wrap justify-center gap-2">
                  {availableTools.map((tool) => (
                    <button
                      key={tool.id}
                      onClick={() => handleContinueWithTool(tool.id)}
                      disabled={isGeneratingURL}
                      className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-500 transition-colors disabled:opacity-50 text-sm"
                    >
                      {isGeneratingURL ? '準備中...' : tool.title}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 処理中表示 */}
          {isProcessing && (
            <div className="text-center">
              <div className="inline-block bg-blue-900/50 border border-blue-600 rounded-lg px-6 py-3">
                <p className="text-blue-300 font-medium">処理中...</p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}