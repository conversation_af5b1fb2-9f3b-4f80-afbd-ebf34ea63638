'use client';

import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';

interface RecaptchaProviderProps {
  children: React.ReactNode;
}

export default function RecaptchaProvider({ children }: RecaptchaProviderProps) {
  // reCAPTCHA有効化フラグ
  const isRecaptchaEnabled = process.env.NEXT_PUBLIC_RECAPTCHA_ENABLED === 'true';
  const reCaptchaKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;

  // reCAPTCHAが無効化されている場合は、ライブラリを読み込まない
  if (!isRecaptchaEnabled) {
    return <>{children}</>;
  }

  // reCAPTCHA有効だがキーが設定されていない場合
  if (!reCaptchaKey) {
    console.warn('reCAPTCHA is enabled but site key is not configured');
    return <>{children}</>;
  }

  return (
    <GoogleReCaptchaProvider
      reCaptchaKey={reCaptchaKey}
      language="ja"
      scriptProps={{
        async: true,
        defer: true,
        appendTo: 'head',
      }}
    >
      {children}
    </GoogleReCaptchaProvider>
  );
}