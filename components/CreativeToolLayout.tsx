import { ReactNode } from 'react';
import Navigation from './Navigation';
import Footer from './Footer';
import GameCard from './GameCard';
import { toolsData } from '../data/gameData';

interface CreativeToolLayoutProps {
  title: string;
  subtitle: string;
  description: string;
  children: ReactNode;
  currentToolId: string;
  toolUsageSteps?: {
    icon: string;
    title: string;
    description: string;
  }[];
}

export default function CreativeToolLayout({
  title,
  subtitle,
  description,
  children,
  currentToolId,
  toolUsageSteps
}: CreativeToolLayoutProps) {
  // 現在のツール以外を表示
  const otherTools = toolsData.filter(tool => tool.id !== currentToolId);
  return (
    <main className="bg-gray-900/0 text-white">
      <Navigation />
      
      {/* パララックス背景 */}
      <div className="parallax-bg" id="parallax-bg"></div>

      {/* 背景を暗くするオーバーレイ */}
      <div className="fixed inset-0 bg-black/10 z-0"></div>

      {/* メインコンテンツ */}
      <div className="parallax-wrapper">
        <section className="bg-red-900 min-h-screen pt-20 md:pt-28 relative z-10">
          {/* コンパクトなページタイトル */}
          <div className="max-w-6xl mx-auto px-6 md:px-12 mb-8">
            <div className="text-center">
              <h1 className="text-2xl md:text-4xl montserrat-bold text-white mb-3">
                {title}
              </h1>
              <p className="text-lg md:text-xl text-blue-300 font-light mb-4">
                {subtitle}
              </p>
              <p className="text-sm md:text-base text-gray-300 leading-relaxed max-w-3xl mx-auto">
                {description}
              </p>
            </div>
          </div>

          {/* ツールエリア */}
          <div className="w-full bg-gray-900/80">
            <div className="max-w-4xl mx-auto px-6 md:px-12 py-16">
              {children}
            </div>
          </div>

          {/* 使い方セクション（オプション） */}
          {toolUsageSteps && (
            <div className="w-full bg-gray-900/60">
              <div className="max-w-4xl mx-auto px-6 md:px-12 py-8">
                <h2 className="text-lg md:text-xl montserrat-bold text-white mb-4 text-center">
                  使い方
                </h2>
                {/* デスクトップ: 3列、モバイル: 2列（アイコン左、説明右） */}
                <div className="grid md:grid-cols-3 grid-cols-1 gap-4">
                  {toolUsageSteps.map((step, index) => (
                    <div key={index} className="md:text-center">
                      {/* デスクトップレイアウト */}
                      <div className="hidden md:block">
                        <div className="text-2xl mb-2">{step.icon}</div>
                        <h3 className="text-sm font-bold text-white mb-1">{step.title}</h3>
                        <p className="text-gray-300 text-xs">
                          {step.description}
                        </p>
                      </div>
                      {/* モバイルレイアウト（2列: アイコン左、説明右） */}
                      <div className="flex items-start gap-3 md:hidden">
                        <div className="text-2xl flex-shrink-0">{step.icon}</div>
                        <div className="flex-1">
                          <h3 className="text-sm font-bold text-white mb-1">{step.title}</h3>
                          <p className="text-gray-300 text-xs">
                            {step.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </section>
      </div>

      {/* SEO対策: 関連ツール・内部リンク強化 */}
      {otherTools.length > 0 && (
        <div className="w-full bg-gray-900/80">
          <div className="max-w-6xl mx-auto px-6 md:px-12 py-16">
            {/* 関連ツール紹介セクション */}
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                他の便利な画像処理ツール
              </h2>
              <p className="text-gray-300 text-lg mb-8">
                {currentToolId.includes('image') || currentToolId.includes('square') || currentToolId.includes('jpeg') 
                  ? '画像編集・最適化作業をもっと効率的に。無料で使える画像処理ツール集'
                  : 'Web制作・コンテンツ作成に役立つ無料ツール集'
                }
              </p>
            </div>

            <div className={`grid gap-6 ${otherTools.length === 1 ? 'max-w-md mx-auto' : 'sm:grid-cols-3'} mb-12`}>
              {otherTools.map((tool) => (
                <GameCard key={tool.id} game={tool} />
              ))}
            </div>

            {/* 内部リンク強化 */}
            <div className="text-center space-y-4">
              <div className="flex flex-wrap justify-center gap-4 mb-6">
                <a 
                  href="/games/image-resizer/" 
                  className="text-blue-400 hover:text-blue-300 underline"
                  title="画像リサイズツール - 無料でオンライン画像サイズ変更"
                >
                  画像リサイズ
                </a>
                <span className="text-gray-500">|</span>
                <a 
                  href="/games/jpeg-optimizer/" 
                  className="text-blue-400 hover:text-blue-300 underline"
                  title="JPEG圧縮・最適化ツール - 無料で50KB以下に自動圧縮"
                >
                  JPEG圧縮
                </a>
                <span className="text-gray-500">|</span>
                <a 
                  href="/games/square-cropper/" 
                  className="text-blue-400 hover:text-blue-300 underline"
                  title="正方形クロップツール - 無料で画像を正方形に切り抜き"
                >
                  正方形クロップ
                </a>
                <span className="text-gray-500">|</span>
                <a 
                  href="/games/webp-optimizer/" 
                  className="text-blue-400 hover:text-blue-300 underline"
                  title="WebP変換・最適化ツール - 無料で50KB以下に自動圧縮"
                >
                  WebP変換
                </a>
                <span className="text-gray-500">|</span>
                <a 
                  href="/games/text-diff/" 
                  className="text-blue-400 hover:text-blue-300 underline"
                  title="テキスト比較・差分ツール - 無料で文章の違いを可視化"
                >
                  テキスト比較
                </a>
              </div>
              
              <a href="/games/" className="hero-cta-button inline-flex items-center">
                <span className="text-xl mr-2">🛠️</span>
                全ての無料ツール・ゲーム一覧を見る
              </a>
            </div>
          </div>
        </div>
      )}

      <Footer />
    </main>
  );
}