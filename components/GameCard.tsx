'use client';

import { useState } from 'react';
import Image from 'next/image';
import { GameData } from '../data/gameData';


interface GameAuthProps {
  onPasswordSubmit: (password: string) => void;
  error?: string;
  onClose: () => void;
}

function GameAuthModal({ onPasswordSubmit, error = '', onClose }: GameAuthProps) {
  const [password, setPassword] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onPasswordSubmit(password);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
      <div className="bg-gray-800 p-8 rounded-2xl max-w-md w-full mx-4 border border-gray-600">
        <div className="text-center mb-6">
          <h3 className="text-xl font-bold text-white mb-2">🔒 ゲームアクセス</h3>
          <p className="text-gray-300 text-sm">
            このゲームは限定公開です。<br />
            パスワードを入力してください。
          </p>
        </div>
        
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="パスワードを入力"
              className="w-full px-4 py-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:border-purple-500 transition-colors"
              required
            />
          </div>
          
          {error && (
            <div className="mb-4 p-3 bg-red-900/50 border border-red-600 rounded-lg">
              <p className="text-red-300 text-sm text-center">{error}</p>
            </div>
          )}
          
          <div className="flex gap-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
            >
              キャンセル
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-500 transition-colors"
            >
              アクセス
            </button>
          </div>
        </form>
        
        <div className="text-center mt-3">
          <a 
            href="/contact?message=ゲームコンテンツについて問い合わせ（ほかにあれば追記下さい）"
            className="text-sm text-gray-400 hover:text-blue-400 transition-colors underline"
          >
            パスワードがわからない場合は<br />こちらからお問い合わせください
          </a>
        </div>
      </div>
    </div>
  );
}

interface GameCardProps {
  game: GameData;
}

export default function GameCard({ game }: GameCardProps) {
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authError, setAuthError] = useState('');

  const handleGameAccess = () => {
    if (game.status === '限定公開') {
      setShowAuthModal(true);
    } else {
      // 公開ゲーム・ツールのアクセス
      if (game.link.external) {
        window.open(game.link.url, '_blank');
      } else {
        // クリエイティブツールは通常のページ遷移
        const isCreativeTool = game.tags.includes('ツール') || game.tags.includes('画像処理') || game.tags.includes('クリエイティブ');
        if (isCreativeTool) {
          window.location.href = game.link.url;
        } else {
          // ゲームページはフルスクリーンモードで開く
          window.open(game.link.url, '_blank', 'fullscreen=yes,menubar=no,toolbar=no,location=no,status=no,scrollbars=yes,resizable=yes');
        }
      }
    }
  };

  const handlePasswordSubmit = (password: string) => {
    // Use environment variable for password
    const validPassword = process.env.NEXT_PUBLIC_PASSWORD;
    
    if (password === validPassword) {
      // Navigate to the game
      if (game.link.external) {
        window.open(game.link.url, '_blank');
      } else {
        // クリエイティブツールは通常のページ遷移
        const isCreativeTool = game.tags.includes('ツール') || game.tags.includes('画像処理') || game.tags.includes('クリエイティブ');
        if (isCreativeTool) {
          window.location.href = game.link.url;
        } else {
          // ゲームページはフルスクリーンモードで開く
          window.open(game.link.url, '_blank', 'fullscreen=yes,menubar=no,toolbar=no,location=no,status=no,scrollbars=yes,resizable=yes');
        }
      }
      setShowAuthModal(false);
      setAuthError('');
    } else {
      setAuthError('パスワードが正しくありません。');
    }
  };

  const linkIcon = game.link.external ? 
    <span className="text-xs ml-2">↗</span> : 
    <span className="text-xs ml-2">▶</span>;

  return (
    <div id={`game-${game.id}`} className="game-card">
      <div 
        className="bg-gray-800 bg-opacity-70 rounded-2xl overflow-hidden border border-gray-700 hover:border-purple-500 transition-all duration-300 hover:transform hover:scale-105 cursor-pointer"
        onClick={handleGameAccess}
      >
        {/* モバイル: 横並び、640px以上: 縦並び */}
        <div className="flex flex-row sm:flex-col min-h-24 sm:min-h-0">
          {/* 画像エリア */}
          <div className="relative w-24 h-24 sm:w-full sm:h-32 flex-shrink-0">
            <Image 
              src={`/img/${game.image}`} 
              alt={game.title}
              fill
              className="object-cover" 
              sizes="(max-width: 1024px) 128px, 100vw"
            />
            {game.status === '限定公開' && (
              <div className="absolute top-2 right-2 sm:top-4 sm:right-4">
                <span className="bg-orange-500 text-white text-xs px-2 py-1 sm:px-3 sm:py-1 rounded-full font-semibold">
                  🔒 限定公開
                </span>
              </div>
            )}
            {game.status === '公開' && (
              <div className="absolute top-2 right-2 sm:top-4 sm:right-4">
                <span className="bg-green-500 text-white text-xs px-2 py-1 sm:px-3 sm:py-1 rounded-full font-semibold">
                  ✓ 公開中
                </span>
              </div>
            )}
          </div>
        
          {/* コンテンツエリア */}
          <div className="px-4 py-2 sm:p-4 flex-1 flex flex-col justify-between">
            <div className="mb-1 sm:mb-3">
              <h3 className="text-lg sm:text-xl font-bold text-white mb-1 montserrat-bold line-clamp-1">
                {game.title}
              </h3>
              <p className="text-purple-300 leading-relaxed text-sm line-clamp-1">
                {game.description}
              </p>
            </div>

            <div className="mb-0 sm:mb-4">
              <div className="flex gap-1 overflow-x-auto scrollbar-hide">
                {game.tags.map((tag, index) => (
                  <span 
                    key={index}
                    className="text-xs text-white bg-purple-600 rounded-full px-2 py-1 whitespace-nowrap flex-shrink-0"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>

            <div className="hidden sm:flex justify-center">
              {game.status === '限定公開' ? (
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowAuthModal(true);
                  }}
                  className="hero-cta-button inline-flex items-center justify-center w-full text-sm px-3 py-2"
                >
                  🔒 {game.link.text}
                  {linkIcon}
                </button>
              ) : (
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    if (game.link.external) {
                      window.open(game.link.url, '_blank');
                    } else {
                      // クリエイティブツールは通常のページ遷移
                      const isCreativeTool = game.tags.includes('ツール') || game.tags.includes('画像処理') || game.tags.includes('クリエイティブ');
                      if (isCreativeTool) {
                        window.location.href = game.link.url;
                      } else {
                        // ゲームページはフルスクリーンモードで開く
                        window.open(game.link.url, '_blank', 'fullscreen=yes,menubar=no,toolbar=no,location=no,status=no,scrollbars=yes,resizable=yes');
                      }
                    }
                  }}
                  className="hero-cta-button inline-flex items-center justify-center w-full text-sm px-3 py-2"
                >
                  {game.link.text}
                  {linkIcon}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {showAuthModal && (
        <GameAuthModal
          onPasswordSubmit={handlePasswordSubmit}
          error={authError}
          onClose={() => {
            setShowAuthModal(false);
            setAuthError('');
          }}
        />
      )}
    </div>
  );
}

