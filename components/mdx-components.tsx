import type { MDXComponents } from 'mdx/types'
import Image from 'next/image'
import Link from 'next/link'
import Callout from '../app/articles/components/Callout'
import CompanyProfile from '../app/articles/components/CompanyProfile'
import YouTubeEmbed from '../app/articles/components/YouTubeEmbed'

// MDXコンポーネントの設定
export function getMDXComponents(): MDXComponents {
  return {
    // シンプルな見出しスタイル
    h1: ({ children }) => (
      <h1 className="text-2xl font-bold text-white mb-6 mt-8 first:mt-0">
        {children}
      </h1>
    ),
    h2: ({ children }) => (
      <h2 className="text-2xl font-bold text-white mb-6 mt-8">
        {children}
      </h2>
    ),
    h3: ({ children }) => (
      <h3 className="text-xl font-bold text-white mb-4 mt-6">
        {children}
      </h3>
    ),
    h4: ({ children }) => (
      <h4 className="text-lg font-semibold text-white mb-3 mt-4">
        {children}
      </h4>
    ),

    // パラグラフ
    p: ({ children }) => (
      <div className="text-gray-300 mb-4 leading-relaxed">
        {children}
      </div>
    ),

    // リスト
    ul: ({ children }) => (
      <ul className="list-disc list-inside mb-4 space-y-1 text-gray-300">
        {children}
      </ul>
    ),
    ol: ({ children }) => (
      <ol className="list-decimal list-inside mb-4 space-y-1 text-gray-300">
        {children}
      </ol>
    ),
    li: ({ children }) => (
      <li className="text-gray-300">
        {children}
      </li>
    ),

    // 引用
    blockquote: ({ children }) => (
      <blockquote className="border-l-4 border-blue-500 pl-4 py-2 my-4 italic text-gray-400">
        {children}
      </blockquote>
    ),

    // 強調
    strong: ({ children }) => (
      <strong className="text-white font-semibold">
        {children}
      </strong>
    ),

    // リンク
    a: ({ href, children }) => {
      if (href?.startsWith('http')) {
        return (
          <a 
            href={href} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-400 hover:text-blue-300 underline"
          >
            {children}
          </a>
        )
      }
      return (
        <Link 
          href={href || ''} 
          className="text-blue-400 hover:text-blue-300 underline"
        >
          {children}
        </Link>
      )
    },

    // 水平線
    hr: () => (
      <hr className="my-6 border-gray-600" />
    ),

    // YouTube埋め込み
    iframe: ({ src, width, height, ...props }) => {
      // YouTubeの場合はレスポンシブ対応
      if (src && (src.includes('youtube.com') || src.includes('youtu.be'))) {
        return <YouTubeEmbed src={src} {...props} />;
      }
      // その他のiframe
      return (
        <div className="mb-6 w-full overflow-hidden">
          <iframe
            src={src}
            className="w-full max-w-full rounded-lg"
            width={width}
            height={height}
            {...props}
          />
        </div>
      );
    },

    // Next.js Image コンポーネント
    Image: ({ src, alt, ...props }) => (
      <div className="mb-6">
        <Image
          src={src || ''}
          alt={alt || ''}
          width={800}
          height={450}
          className="w-full rounded-lg"
          {...props}
        />
      </div>
    ),

    // キャプション付き画像コンポーネント
    ImageWithCaption: ({ src, alt, caption, ...props }) => (
      <figure className="mb-6">
        <Image
          src={src || ''}
          alt={alt || ''}
          width={800}
          height={450}
          className="w-full rounded-lg"
          {...props}
        />
        <figcaption className="text-xs text-gray-500 mt-2 text-center">
          {caption}
        </figcaption>
      </figure>
    ),

    // 課題・成果セクション（背景画像付き）
    ChallengeResultBox: ({ challenge, result, backgroundImage }) => (
      <div className="bg-gray-800 p-6 border border-gray-700 mb-8 relative overflow-hidden">
        {backgroundImage && (
          <div 
            className="absolute inset-0 opacity-20"
            style={{
              backgroundImage: `url(${backgroundImage})`,
              backgroundPosition: 'top -80px right -100px',
              backgroundRepeat: 'no-repeat',
              backgroundSize: '400px auto'
            }}
          />
        )}
        <div className="relative z-10">
          {challenge && (
            <div className="mb-4">
              <h5 className="text-sm font-bold text-blue-400 mb-2">📌 課題</h5>
              <p className="text-gray-300 text-sm">{challenge}</p>
            </div>
          )}
          
          {result && (
            <div>
              <h5 className="text-sm font-bold text-green-400 mb-2">✨ 成果</h5>
              <p className="text-gray-300 text-sm">{result}</p>
            </div>
          )}
        </div>
      </div>
    ),

    // カスタムコンポーネント
    Callout,
    CompanyProfile,
    YouTubeEmbed,
  }
}