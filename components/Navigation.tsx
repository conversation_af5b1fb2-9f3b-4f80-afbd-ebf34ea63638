'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className="fixed top-0 w-full z-50">
      <div className="max-w-7xl mx-auto px-6 py-6 md:px-12 py-12">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2 text-lg text-white montserrat-bold tracking-wide">
            <Link href="/">SPACE COWBOY</Link>
          </div>

          <div className="md:hidden">
            <button
              id="menu-button"
              className="focus:outline-none text-white p-2"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="ナビゲーションメニューを開く"
              aria-expanded={isMenuOpen}
              aria-controls="mobile-menu"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                  d="M4 6h16M4 12h16m-7 6h7"></path>
              </svg>
            </button>
          </div>
          
          <div className="hidden md:flex space-x-10">
            <Link href="/company"
              className="text-sm font-semibold text-white hover:text-blue-400 transition-colors montserrat-regular">COMPANY</Link>
            <Link href="/service"
              className="text-sm font-semibold text-white hover:text-blue-400 transition-colors montserrat-regular">SERVICE</Link>
            <Link href="/articles"
              className="text-sm font-semibold text-white hover:text-blue-400 transition-colors montserrat-regular">ARTICLES</Link>
            <Link href="/contact"
              className="text-sm font-semibold text-white hover:text-blue-400 transition-colors montserrat-regular">CONTACT</Link>
            <Link href="/games"
              className="text-sm font-semibold text-white hover:text-blue-400 transition-colors montserrat-regular">🎮 LAB</Link>
          </div>
          
          {/* Mobile menu */}
          <div id="mobile-menu"
            className={`${isMenuOpen ? 'block' : 'hidden'} md:hidden absolute top-full left-0 w-full bg-black/85 backdrop-blur-md border-t border-gray-700/50 shadow-2xl z-50`}>
            <div className="flex flex-col px-6 py-8">
              <Link href="/company"
                className="group flex items-center justify-between py-4 text-base font-light text-white hover:text-blue-400 transition-all duration-300 montserrat-regular border-b border-gray-700/30 hover:border-blue-400/50">
                <span>COMPANY</span>
                <svg className="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors duration-300"
                  fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7">
                  </path>
                </svg>
              </Link>
              <Link href="/service"
                className="group flex items-center justify-between py-4 text-base font-light text-white hover:text-blue-400 transition-all duration-300 montserrat-regular border-b border-gray-700/30 hover:border-blue-400/50">
                <span>SERVICE</span>
                <svg className="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors duration-300"
                  fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7">
                  </path>
                </svg>
              </Link>
              <Link href="/articles"
                className="group flex items-center justify-between py-4 text-base font-light text-white hover:text-blue-400 transition-all duration-300 montserrat-regular border-b border-gray-700/30 hover:border-blue-400/50">
                <span>ARTICLES</span>
                <svg className="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors duration-300"
                  fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7">
                  </path>
                </svg>
              </Link>
              <Link href="/contact"
                className="group flex items-center justify-between py-4 text-base font-light text-white hover:text-blue-400 transition-all duration-300 montserrat-regular border-b border-gray-700/30 hover:border-blue-400/50">
                <span>CONTACT</span>
                <svg className="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors duration-300"
                  fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7">
                  </path>
                </svg>
              </Link>
              <Link href="/games"
                className="group flex items-center justify-between py-4 text-base font-light text-white hover:text-blue-400 transition-all duration-300 montserrat-regular">
                <span>🎮 LAB</span>
                <svg className="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors duration-300"
                  fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7">
                  </path>
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}