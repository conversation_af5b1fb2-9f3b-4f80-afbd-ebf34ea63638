import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="text-white text-sm mt-20">
      <div className="max-w-6xl mx-auto px-8 grid grid-cols-1 md:grid-cols-4 gap-8">

        {/* 左：会社名・住所 */}
        <div>
          <h2 className="text-xl montserrat-bold mb-2">SPACE COWBOY</h2>
          <p className="text-xs mb-1">株式会社スペースカウボーイ</p>
          <p className="text-xs text-gray-400 mb-1">〒810-0001</p>
          <p className="text-xs text-gray-400 mb-1">福岡市中央区天神4-6-28 天神ファーストビル7F</p>
        </div>

        {/* 中央左：ナビゲーション */}
        <div>
          <ul className="space-y-1">
            <li><Link href="/company" className="montserrat-bold hover:underline">COMPANY</Link></li>
            <li><Link href="/service" className="montserrat-bold hover:underline">SERVICE</Link>
              <ul className="ml-4 mt-1 space-y-1">
                <li><Link href="/service#space-division"
                  className="montserrat-regular text-sm text-gray-400 hover:underline">—
                  SPACE
                  DIVISION</Link></li>
                <li><Link href="/service#creative-division"
                  className="montserrat-regular text-sm text-gray-400 hover:underline">— CREATIVE
                  DIVISION</Link></li>
              </ul>
            </li>
            <li><Link href="/articles" className="montserrat-bold hover:underline">ARTICLES</Link></li>
            <li><Link href="/contact" className="montserrat-bold hover:underline">CONTACT</Link></li>
            <li><Link href="/games" className="montserrat-bold hover:underline">🎮 LAB</Link></li>
          </ul>
        </div>

        {/* 空のカラム */}
        <div></div>

        {/* 右：SNS */}
        <div className="text-right">
          <ul className="space-y-2">
            <li className="flex items-center gap-2 justify-end">
              {/* Xアイコン */}
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path
                  d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
              </svg>
              <a href="https://x.com/cutkey5" className="montserrat-regular hover:underline"
                target="_blank">X(twitter)</a>
            </li>
          </ul>
        </div>

      </div>

      {/* 下部：コピーライト */}
      <div className="max-w-6xl mx-auto px-6 text-xs text-gray-500 py-12">
        <div className="border-t border-gray-600 pt-8">
          <div
            className="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
            <div className="flex flex-col md:flex-row gap-4">
              <Link href="/privacy" className="hover:underline">プライバシーポリシー</Link>
              <Link href="/terms" className="hover:underline">利用規約</Link>
              <Link href="/commercial-law" className="hover:underline">特定商取引法に基づく表記</Link>
            </div>
            <div>
              &copy;2025 SPACE COWBOY .Inc
            </div>
          </div>
        </div>
      </div>

    </footer>
  );
}