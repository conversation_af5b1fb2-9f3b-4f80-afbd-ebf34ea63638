'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { getImageFromURL, base64ToBlob, blobToFile, generateToolURL, getAvailableImageTools } from '../utils/imageUtils';
import { trackToolStart, trackToolComplete, trackDownload, trackToolIntegration, trackError } from '../lib/gtag';

interface OptimizedResult {
  blob: Blob;
  size: number;
  dimensions: {
    width: number;
    height: number;
  };
}

export default function WebpOptimizer() {
  const [dragActive, setDragActive] = useState(false);
  const [originalFile, setOriginalFile] = useState<File | null>(null);
  const [originalPreview, setOriginalPreview] = useState<string>('');
  const [optimizedResult, setOptimizedResult] = useState<OptimizedResult | null>(null);
  const [optimizedPreview, setOptimizedPreview] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string>('');
  const [sourceToolInfo, setSourceToolInfo] = useState<{tool: string, message: string} | null>(null);
  const [isGeneratingURL, setIsGeneratingURL] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const availableTools = getAvailableImageTools('webp-optimizer');

  // URLパラメータから画像を自動読み込み
  useEffect(() => {
    const loadImageFromURL = async () => {
      const imageData = getImageFromURL();
      if (imageData) {
        try {
          const blob = base64ToBlob(imageData.data, 'image/png');
          const file = blobToFile(blob, 'tool-image.png');
          
          // ソース情報を設定
          if (imageData.source === 'square-cropper') {
            setSourceToolInfo({
              tool: '正方形クロップツール',
              message: '正方形にクロップされた画像を受け取りました'
            });
          } else if (imageData.source === 'image-resizer') {
            setSourceToolInfo({
              tool: '画像リサイズツール',
              message: 'リサイズされた画像を受け取りました'
            });
          } else if (imageData.source === 'jpeg-optimizer') {
            setSourceToolInfo({
              tool: 'JPEG最適化ツール',
              message: '最適化された画像を受け取りました'
            });
          }
          
          // ファイル検証
          if (!file.type.startsWith('image/')) {
            setError('画像ファイルのみ対応しています。');
            return;
          }
          if (file.size > 10 * 1024 * 1024) {
            setError('ファイルサイズが大きすぎます（10MB以下にしてください）。');
            return;
          }

          setError('');
          setIsProcessing(true);
          const startTime = Date.now();
          
          // アナリティクス: ツール使用開始
          trackToolStart('webp-optimizer');
          
          try {
            // プレビュー作成
            const originalPreviewUrl = URL.createObjectURL(file);
            setOriginalFile(file);
            setOriginalPreview(originalPreviewUrl);

            // WebP最適化処理（インライン実装）
            const result = await new Promise<OptimizedResult>((resolve, reject) => {
              const img = new Image();
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');

              if (!ctx) {
                reject(new Error('Canvas context not available'));
                return;
              }

              img.onload = () => {
                // リサイズ計算
                let { width, height } = img;
                const maxWidth = 1000;
                
                if (width > maxWidth) {
                  height = (height * maxWidth) / width;
                  width = maxWidth;
                }

                canvas.width = width;
                canvas.height = height;

                // 画像を描画
                ctx.drawImage(img, 0, 0, width, height);

                // 品質を調整しながら50KB以下にする
                const tryCompress = (quality: number): void => {
                  canvas.toBlob(
                    (blob) => {
                      if (!blob) {
                        reject(new Error('Failed to create blob'));
                        return;
                      }

                      const targetSize = 50 * 1024; // 50KB

                      if (blob.size <= targetSize || quality <= 0.1) {
                        resolve({
                          blob,
                          size: blob.size,
                          dimensions: { width, height }
                        });
                      } else {
                        // サイズがまだ大きい場合は品質を下げて再試行
                        const newQuality = Math.max(0.1, quality - 0.1);
                        setTimeout(() => tryCompress(newQuality), 10);
                      }
                    },
                    'image/webp',
                    quality
                  );
                };

                // 品質0.9から開始
                tryCompress(0.9);
              };

              img.onerror = () => {
                reject(new Error('画像の読み込みに失敗しました'));
              };

              img.src = URL.createObjectURL(file);
            });
            
            const optimizedPreviewUrl = URL.createObjectURL(result.blob);
            
            setOptimizedResult(result);
            setOptimizedPreview(optimizedPreviewUrl);
            
            // アナリティクス: 処理完了
            const processingTime = Date.now() - startTime;
            trackToolComplete('webp-optimizer', processingTime);
          } catch (err) {
            const errorMessage = err instanceof Error ? err.message : '処理中にエラーが発生しました';
            setError(errorMessage);
            trackError('webp-optimizer', 'processing_error', errorMessage);
          } finally {
            setIsProcessing(false);
          }
          
          // URLのクエリパラメータをクリア
          if (window.history.replaceState) {
            window.history.replaceState({}, document.title, window.location.pathname);
          }
        } catch (error) {
          setError('前のツールからの画像データの読み込みに失敗しました。');
          console.error('Failed to load image from URL:', error);
        }
      }
    };

    loadImageFromURL();
  }, []);

  // ファイルの検証
  const validateFile = (file: File): boolean => {
    if (!file.type.startsWith('image/')) {
      setError('画像ファイルのみ対応しています。');
      return false;
    }
    if (file.size > 10 * 1024 * 1024) { // 10MB制限
      setError('ファイルサイズが大きすぎます（10MB以下にしてください）。');
      return false;
    }
    return true;
  };

  // ファイル処理
  const processFile = useCallback(async (file: File) => {
    if (!validateFile(file)) return;

    setError('');
    setIsProcessing(true);
    const startTime = Date.now();
    
    // アナリティクス: ツール使用開始
    trackToolStart('webp-optimizer');
    
    try {
      // プレビュー作成
      const originalPreviewUrl = URL.createObjectURL(file);
      setOriginalFile(file);
      setOriginalPreview(originalPreviewUrl);

      // WebP最適化処理（インライン実装）
      const result = await new Promise<OptimizedResult>((resolve, reject) => {
        const img = new Image();
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('Canvas context not available'));
          return;
        }

        img.onload = () => {
          // リサイズ計算
          let { width, height } = img;
          const maxWidth = 1000;
          
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }

          canvas.width = width;
          canvas.height = height;

          // 画像を描画
          ctx.drawImage(img, 0, 0, width, height);

          // 品質を調整しながら50KB以下にする
          const tryCompress = (quality: number): void => {
            canvas.toBlob(
              (blob) => {
                if (!blob) {
                  reject(new Error('Failed to create blob'));
                  return;
                }

                const targetSize = 50 * 1024; // 50KB

                if (blob.size <= targetSize || quality <= 0.1) {
                  resolve({
                    blob,
                    size: blob.size,
                    dimensions: { width, height }
                  });
                } else {
                  // サイズがまだ大きい場合は品質を下げて再試行
                  const newQuality = Math.max(0.1, quality - 0.1);
                  setTimeout(() => tryCompress(newQuality), 10);
                }
              },
              'image/webp',
              quality
            );
          };

          // 品質0.9から開始
          tryCompress(0.9);
        };

        img.onerror = () => {
          reject(new Error('画像の読み込みに失敗しました'));
        };

        img.src = URL.createObjectURL(file);
      });
      
      const optimizedPreviewUrl = URL.createObjectURL(result.blob);
      
      setOptimizedResult(result);
      setOptimizedPreview(optimizedPreviewUrl);
      
      // アナリティクス: 処理完了
      const processingTime = Date.now() - startTime;
      trackToolComplete('webp-optimizer', processingTime);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '処理中にエラーが発生しました';
      setError(errorMessage);
      trackError('webp-optimizer', 'processing_error', errorMessage);
    } finally {
      setIsProcessing(false);
    }
  }, []);

  // ドラッグ&ドロップ処理
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      processFile(e.dataTransfer.files[0]);
    }
  }, [processFile]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  // ファイル選択処理
  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      processFile(e.target.files[0]);
    }
  }, [processFile]);

  // ダウンロード処理
  const handleDownload = useCallback(() => {
    if (!optimizedResult || !originalFile) return;

    const link = document.createElement('a');
    link.href = URL.createObjectURL(optimizedResult.blob);
    const originalName = originalFile.name.replace(/\.[^/.]+$/, '');
    link.download = `${originalName}.webp`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // アナリティクス: ダウンロード追跡
    const fileType = originalFile.type || 'unknown';
    trackDownload('webp-optimizer', fileType, optimizedResult.size);
  }, [optimizedResult, originalFile]);

  // 他のツールで続行
  const handleContinueWithTool = useCallback(async (toolId: string) => {
    if (!optimizedResult) return;

    setIsGeneratingURL(true);
    try {
      // アナリティクス: ツール間連携追跡
      trackToolIntegration('webp-optimizer', toolId);
      
      const url = await generateToolURL(toolId, optimizedResult.blob, 'webp-optimizer');
      window.location.href = url;
    } catch (error) {
      const errorMessage = 'ツール連携でエラーが発生しました。';
      setError(errorMessage);
      console.error('Tool integration error:', error);
      trackError('webp-optimizer', 'tool_integration_error', errorMessage);
    } finally {
      setIsGeneratingURL(false);
    }
  }, [optimizedResult]);

  // リセット処理
  const handleReset = useCallback(() => {
    setOriginalFile(null);
    setOriginalPreview('');
    setOptimizedResult(null);
    setOptimizedPreview('');
    setError('');
    setSourceToolInfo(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* アップロードエリア */}
      <div
        className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${
          dragActive
            ? 'border-blue-400 bg-blue-400/10'
            : 'border-gray-600 hover:border-gray-500'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <div className="text-6xl mb-4">🖼️</div>
        <h3 className="text-xl font-bold text-white mb-2">
          画像ファイルをドロップまたはクリック
        </h3>
        <p className="text-gray-400 text-sm mb-4">
          JPEG/PNG/GIF → WebP形式に自動変換（50KB以下）
        </p>
        <p className="text-gray-500 text-xs">
          対応形式: JPEG/PNG/GIF（最大10MB）
        </p>
      </div>

      {/* ソースツール情報表示 */}
      {sourceToolInfo && (
        <div className="mt-6 p-4 bg-green-900/50 border border-green-600 rounded-lg">
          <div className="text-center">
            <div className="text-2xl mb-2">✅</div>
            <p className="text-green-300 font-medium">{sourceToolInfo.tool}から画像を受け取りました</p>
            <p className="text-green-400 text-sm mt-1">{sourceToolInfo.message}</p>
          </div>
        </div>
      )}

      {/* エラー表示 */}
      {error && (
        <div className="mt-6 p-4 bg-red-900/50 border border-red-600 rounded-lg">
          <p className="text-red-300 text-sm text-center">{error}</p>
        </div>
      )}

      {/* 処理中表示 */}
      {isProcessing && (
        <div className="mt-6 p-8 bg-gray-800 rounded-lg text-center">
          <div className="text-4xl mb-4">⚙️</div>
          <p className="text-white font-medium">WebP形式に変換中...</p>
          <p className="text-gray-400 text-sm mt-2">しばらくお待ちください</p>
        </div>
      )}

      {/* 結果表示 */}
      {originalFile && optimizedResult && !isProcessing && (
        <div className="mt-8 space-y-6">
          {/* Before/After */}
          <div className="grid md:grid-cols-2 gap-6">
            {/* Before */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-bold text-white mb-4 text-center">
                変換前
              </h3>
              <div className="aspect-video bg-gray-700 rounded-lg mb-4 overflow-hidden">
                <img
                  src={originalPreview}
                  alt="Original"
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="text-center text-sm text-gray-400">
                <p>サイズ: {(originalFile.size / 1024).toFixed(1)}KB</p>
                <p>形式: {originalFile.type.split('/')[1]?.toUpperCase()}</p>
              </div>
            </div>

            {/* After */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-bold text-white mb-4 text-center">
                変換後（WebP）
              </h3>
              <div className="aspect-video bg-gray-700 rounded-lg mb-4 overflow-hidden">
                <img
                  src={optimizedPreview}
                  alt="WebP Optimized"
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="text-center text-sm text-gray-400">
                <p>サイズ: {(optimizedResult.size / 1024).toFixed(1)}KB</p>
                <p>
                  解像度: {optimizedResult.dimensions.width} × {optimizedResult.dimensions.height}
                </p>
              </div>
            </div>
          </div>

          {/* アクションボタン */}
          <div className="flex flex-col gap-4">
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <button
                onClick={handleDownload}
                disabled={isGeneratingURL}
                className="hero-cta-button inline-flex items-center disabled:opacity-50"
              >
                <span className="text-xl mr-2">💾</span>
                WebP画像をダウンロード
              </button>
              <button
                onClick={handleReset}
                disabled={isGeneratingURL}
                className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors disabled:opacity-50"
              >
                別の画像を選択
              </button>
            </div>
            
            {/* 他のツールとの連携ボタン */}
            {optimizedResult && availableTools.length > 0 && (
              <div className="border-t border-gray-600 pt-4">
                <p className="text-center text-gray-400 text-sm mb-3">他の画像処理ツールで続行</p>
                <div className="flex flex-wrap justify-center gap-2">
                  {availableTools.map((tool) => (
                    <button
                      key={tool.id}
                      onClick={() => handleContinueWithTool(tool.id)}
                      disabled={isGeneratingURL}
                      className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-500 transition-colors disabled:opacity-50 text-sm"
                    >
                      {isGeneratingURL ? '準備中...' : tool.title}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 圧縮率表示 */}
          <div className="text-center">
            <div className="inline-block bg-green-900/50 border border-green-600 rounded-lg px-6 py-3">
              <p className="text-green-300 font-medium">
                ファイルサイズ削減: {(((originalFile.size - optimizedResult.size) / originalFile.size) * 100).toFixed(1)}%
              </p>
              <p className="text-green-400 text-sm mt-1">
                {(originalFile.size / 1024).toFixed(1)}KB → {(optimizedResult.size / 1024).toFixed(1)}KB（WebP形式）
              </p>
              <p className="text-green-500 text-xs mt-2">
                次世代画像フォーマットでWeb表示速度を大幅改善！
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}