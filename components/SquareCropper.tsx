'use client';

import { useState, useRef, useCallback } from 'react';
import { generateToolURL, getAvailableImageTools } from '../utils/imageUtils';

interface CroppedResult {
  blob: Blob;
  size: number;
  dimensions: {
    width: number;
    height: number;
  };
}

export default function SquareCropper() {
  const [dragActive, setDragActive] = useState(false);
  const [originalFile, setOriginalFile] = useState<File | null>(null);
  const [originalPreview, setOriginalPreview] = useState<string>('');
  const [originalDimensions, setOriginalDimensions] = useState<{width: number, height: number} | null>(null);
  const [croppedResult, setCroppedResult] = useState<CroppedResult | null>(null);
  const [croppedPreview, setCroppedPreview] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string>('');
  const [isGeneratingURL, setIsGeneratingURL] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const availableTools = getAvailableImageTools('square-cropper');

  // ファイルの検証
  const validateFile = (file: File): boolean => {
    if (!file.type.startsWith('image/')) {
      setError('画像ファイルのみ対応しています。');
      return false;
    }
    if (file.size > 10 * 1024 * 1024) { // 10MB制限
      setError('ファイルサイズが大きすぎます（10MB以下にしてください）。');
      return false;
    }
    return true;
  };

  // 画像を正方形にクロップ
  const cropToSquare = useCallback(async (file: File): Promise<CroppedResult> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('Canvas context not available'));
        return;
      }

      img.onload = () => {
        // 短い方の辺を基準にした正方形サイズを決定
        const squareSize = Math.min(img.width, img.height);
        
        // クロップ開始位置を計算（中央から）
        const startX = (img.width - squareSize) / 2;
        const startY = (img.height - squareSize) / 2;

        canvas.width = squareSize;
        canvas.height = squareSize;

        // 高品質でクロップ
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        
        // 元画像の中央部分を正方形で切り取り
        ctx.drawImage(
          img,
          startX, startY, squareSize, squareSize, // 元画像のクロップ範囲
          0, 0, squareSize, squareSize           // キャンバスの描画範囲
        );

        // 元のファイル形式を保持
        const outputFormat = file.type === 'image/png' ? 'image/png' : 'image/jpeg';
        const quality = file.type === 'image/jpeg' ? 0.92 : undefined;

        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Failed to create blob'));
              return;
            }

            resolve({
              blob,
              size: blob.size,
              dimensions: { width: squareSize, height: squareSize }
            });
          },
          outputFormat,
          quality
        );
      };

      img.onerror = () => {
        reject(new Error('画像の読み込みに失敗しました'));
      };

      img.src = URL.createObjectURL(file);
    });
  }, []);

  // ファイル処理
  const processFile = useCallback(async (file: File) => {
    if (!validateFile(file)) return;

    setError('');
    setIsProcessing(true);
    
    try {
      // プレビュー作成と元画像のサイズ取得
      const originalPreviewUrl = URL.createObjectURL(file);
      setOriginalFile(file);
      setOriginalPreview(originalPreviewUrl);

      // 元画像のサイズを取得
      const img = new Image();
      img.onload = async () => {
        setOriginalDimensions({ width: img.width, height: img.height });
        
        try {
          // 正方形クロップ処理
          const result = await cropToSquare(file);
          const croppedPreviewUrl = URL.createObjectURL(result.blob);
          
          setCroppedResult(result);
          setCroppedPreview(croppedPreviewUrl);
        } catch (err) {
          setError(err instanceof Error ? err.message : 'クロップ中にエラーが発生しました');
        } finally {
          setIsProcessing(false);
        }
      };
      img.src = originalPreviewUrl;

    } catch (err) {
      setError(err instanceof Error ? err.message : '処理中にエラーが発生しました');
      setIsProcessing(false);
    }
  }, [cropToSquare]);

  // ドラッグ&ドロップ処理
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      processFile(e.dataTransfer.files[0]);
    }
  }, [processFile]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  // ファイル選択処理
  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      processFile(e.target.files[0]);
    }
  }, [processFile]);

  // ダウンロード処理
  const handleDownload = useCallback(() => {
    if (!croppedResult || !originalFile) return;

    const link = document.createElement('a');
    link.href = URL.createObjectURL(croppedResult.blob);
    link.download = `square_${originalFile.name}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [croppedResult, originalFile]);

  // 他のツールで続行
  const handleContinueWithTool = useCallback(async (toolId: string) => {
    if (!croppedResult) {
      console.log('No cropped result available');
      return;
    }

    console.log('Starting tool integration with cropped result:', croppedResult);
    setIsGeneratingURL(true);
    try {
      console.log(`Generating URL for ${toolId} tool...`);
      const url = await generateToolURL(toolId, croppedResult.blob, 'square-cropper');
      console.log('Generated URL:', url);
      console.log('Navigating to:', url);
      window.location.href = url;
    } catch (error) {
      console.error('Tool integration error:', error);
      setError('ツール連携でエラーが発生しました。');
    } finally {
      setIsGeneratingURL(false);
    }
  }, [croppedResult]);

  // リセット処理
  const handleReset = useCallback(() => {
    setOriginalFile(null);
    setOriginalPreview('');
    setOriginalDimensions(null);
    setCroppedResult(null);
    setCroppedPreview('');
    setError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  return (
    <div className="w-full mx-auto">
      {/* アップロードエリア */}
      <div
        className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${
          dragActive
            ? 'border-blue-400 bg-blue-400/10'
            : 'border-gray-600 hover:border-gray-500'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <div className="text-6xl mb-4">✂️</div>
        <h3 className="text-xl font-bold text-white mb-2">
          画像ファイルをドロップまたはクリック
        </h3>
        <p className="text-gray-400 text-sm mb-4">
          縦長・横長の画像を正方形にクロップします
        </p>
        <p className="text-gray-500 text-xs">
          対応形式: JPEG, PNG, GIF, WebP（最大10MB）
        </p>
      </div>

      {/* エラー表示 */}
      {error && (
        <div className="mt-6 p-4 bg-red-900/50 border border-red-600 rounded-lg">
          <p className="text-red-300 text-sm text-center">{error}</p>
        </div>
      )}

      {/* 画像選択後のプレビューとクロップ結果 */}
      {originalFile && originalDimensions && (
        <div className="mt-8 space-y-6">
          {/* クロップ情報 */}
          <div className="bg-gray-800 rounded-lg p-6 text-center">
            <h3 className="text-lg font-bold text-white mb-4">
              クロップ情報
            </h3>
            <div className="grid md:grid-cols-3 gap-4 text-sm">
              <div>
                <p className="text-gray-400">元画像サイズ</p>
                <p className="text-white font-medium">
                  {originalDimensions.width} × {originalDimensions.height}px
                </p>
              </div>
              <div>
                <p className="text-gray-400">正方形サイズ</p>
                <p className="text-white font-medium">
                  {Math.min(originalDimensions.width, originalDimensions.height)} × {Math.min(originalDimensions.width, originalDimensions.height)}px
                </p>
              </div>
              <div>
                <p className="text-gray-400">クロップ方法</p>
                <p className="text-white font-medium">中央から切り取り</p>
              </div>
            </div>
          </div>

          {/* プレビューエリア */}
          {croppedResult && !isProcessing && (
            <div className="grid md:grid-cols-2 gap-6">
              {/* Before */}
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-bold text-white mb-4 text-center">
                  変更前
                </h3>
                <div className="aspect-video bg-gray-700 rounded-lg mb-4 overflow-hidden">
                  <img
                    src={originalPreview}
                    alt="Original"
                    className="w-full h-full object-contain"
                  />
                </div>
                <div className="text-center text-sm text-gray-400">
                  <p>サイズ: {(originalFile.size / 1024).toFixed(1)}KB</p>
                  <p>解像度: {originalDimensions.width} × {originalDimensions.height}px</p>
                </div>
              </div>

              {/* After */}
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-bold text-white mb-4 text-center">
                  変更後（正方形）
                </h3>
                <div className="aspect-square bg-gray-700 rounded-lg mb-4 overflow-hidden mx-auto max-w-64">
                  <img
                    src={croppedPreview}
                    alt="Cropped"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="text-center text-sm text-gray-400">
                  <p>サイズ: {(croppedResult.size / 1024).toFixed(1)}KB</p>
                  <p>
                    解像度: {croppedResult.dimensions.width} × {croppedResult.dimensions.height}px
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* アクションボタン */}
          <div className="flex flex-col gap-4">
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              {croppedResult && (
                <button
                  onClick={handleDownload}
                  disabled={isProcessing || isGeneratingURL}
                  className="hero-cta-button inline-flex items-center disabled:opacity-50"
                >
                  <span className="text-xl mr-2">💾</span>
                  正方形画像をダウンロード
                </button>
              )}
              <button
                onClick={handleReset}
                disabled={isGeneratingURL}
                className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors disabled:opacity-50"
              >
                別の画像を選択
              </button>
            </div>
            
            {/* 他のツールとの連携ボタン */}
            {croppedResult && availableTools.length > 0 && (
              <div className="border-t border-gray-600 pt-4">
                <p className="text-center text-gray-400 text-sm mb-3">他の画像処理ツールで続行</p>
                <div className="flex flex-wrap justify-center gap-2">
                  {availableTools.map((tool) => (
                    <button
                      key={tool.id}
                      onClick={() => handleContinueWithTool(tool.id)}
                      disabled={isGeneratingURL}
                      className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-500 transition-colors disabled:opacity-50 text-sm"
                    >
                      {isGeneratingURL ? '準備中...' : tool.title}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 処理中表示 */}
          {isProcessing && (
            <div className="text-center">
              <div className="inline-block bg-blue-900/50 border border-blue-600 rounded-lg px-6 py-3">
                <p className="text-blue-300 font-medium">正方形にクロップ中...</p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}