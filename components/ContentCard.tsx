import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

interface ContentCardProps {
  id: string;
  date: string;
  division?: string;
  title: string;
  tags?: string[];
  image: string;
  link?: {
    url: string;
    text: string;
    external?: boolean;
  };
  type?: 'case' | 'article';
}

export default function ContentCard({ 
  id, 
  date, 
  division,
  title, 
  tags, 
  image, 
  link,
  type = 'case'
}: ContentCardProps) {
  const CardWrapper = ({ children }: { children: React.ReactNode }) => {
    if (link) {
      return (
        <Link 
          href={link.url}
          target={link.external ? "_blank" : undefined}
          rel={link.external ? "noopener noreferrer" : undefined}
          className="block"
        >
          {children}
        </Link>
      );
    }
    return <div>{children}</div>;
  };

  return (
    <CardWrapper>
      <div 
        key={id}
        className="case-inner bg-gray-800/50 overflow-hidden hover:transform hover:scale-105 transition-all duration-300 hover:bg-gray-700/50 cursor-pointer"
      >
      {/* モバイル: 横並び、デスクトップ: 縦並び */}
      <div className="flex flex-row md:flex-col">
        {/* 画像 - 右側のコンテンツが長い場合は高くなる */}
        <div className="case-image w-24 min-h-24 max-h-32 md:w-full md:h-40 lg:h-48 relative bg-gray-700 flex-shrink-0 self-stretch md:self-auto">
          <Image
            src={image}
            alt={title}
            fill
            className="object-cover opacity-90 hover:opacity-100 transition-opacity duration-300"
            sizes="(max-width: 768px) 96px, (max-width: 1200px) 50vw, 33vw"
          />
        </div>

        {/* コンテンツ */}
        <div className="case-content px-3 py-0 md:p-5 lg:p-6 flex flex-col flex-1">
          {/* メタ情報 */}
          <div className="case-meta text-xs text-gray-400 uppercase tracking-wide mb-2 leading-tight line-clamp-1">
            {type === 'case' && division && `${date} • ${division}`}
            {type === 'article' && date}
          </div>

          {/* タイトル */}
          <h4 className="case-title text-sm md:text-base lg:text-lg font-bold text-white mb-2 md:mb-4 line-clamp-2 leading-tight">
            {title}
          </h4>


          {/* タグ */}
          {tags && tags.length > 0 && (
            <div className="case-tags flex flex-wrap gap-1 md:gap-2 mb-2 md:mb-4">
              {tags.slice(0, 2).map((tag, index) => (
                <span 
                  key={index}
                  className="text-xs bg-gray-600 text-gray-200 px-2 py-1 rounded-full leading-none"
                >
                  {tag}
                </span>
              ))}
              {tags.length > 2 && (
                <span className="hidden md:inline text-xs bg-gray-600 text-gray-200 px-2 py-1 rounded-full leading-none">
                  {tags[2]}
                </span>
              )}
            </div>
          )}

          {/* リンクテキスト（外部リンクアイコン付き） */}
          {link && (
            <div className="flex items-center text-xs text-blue-400 mt-auto">
              <span>{link.text}</span>
              {link.external && (
                <span className="ml-1">↗</span>
              )}
            </div>
          )}

        </div>
      </div>
      </div>
    </CardWrapper>
  );
}