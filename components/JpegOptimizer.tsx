'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { getImageFromURL, base64ToBlob, blobToFile, generateToolURL, getAvailableImageTools } from '../utils/imageUtils';

interface OptimizedResult {
  blob: Blob;
  size: number;
  dimensions: {
    width: number;
    height: number;
  };
}

export default function JpegOptimizer() {
  const [dragActive, setDragActive] = useState(false);
  const [originalFile, setOriginalFile] = useState<File | null>(null);
  const [originalPreview, setOriginalPreview] = useState<string>('');
  const [optimizedResult, setOptimizedResult] = useState<OptimizedResult | null>(null);
  const [optimizedPreview, setOptimizedPreview] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string>('');
  const [sourceToolInfo, setSourceToolInfo] = useState<{tool: string, message: string} | null>(null);
  const [isGeneratingURL, setIsGeneratingURL] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const availableTools = getAvailableImageTools('jpeg-optimizer');

  // URLパラメータから画像を自動読み込み
  useEffect(() => {
    const loadImageFromURL = async () => {
      const imageData = getImageFromURL();
      if (imageData) {
        try {
          const blob = base64ToBlob(imageData.data, 'image/jpeg');
          const file = blobToFile(blob, 'tool-image.jpg');
          
          // ソース情報を設定
          if (imageData.source === 'square-cropper') {
            setSourceToolInfo({
              tool: '正方形クロップツール',
              message: '正方形にクロップされた画像を受け取りました'
            });
          } else if (imageData.source === 'image-resizer') {
            setSourceToolInfo({
              tool: '画像リサイズツール',
              message: 'リサイズされた画像を受け取りました'
            });
          }
          
          // ファイル検証
          if (!file.type.includes('jpeg') && !file.type.includes('jpg')) {
            setError('JPEGファイルのみ対応しています。');
            return;
          }
          if (file.size > 10 * 1024 * 1024) {
            setError('ファイルサイズが大きすぎます（10MB以下にしてください）。');
            return;
          }

          setError('');
          setIsProcessing(true);
          
          try {
            // プレビュー作成
            const originalPreviewUrl = URL.createObjectURL(file);
            setOriginalFile(file);
            setOriginalPreview(originalPreviewUrl);

            // 最適化処理（インライン実装）
            const result = await new Promise<OptimizedResult>((resolve, reject) => {
              const img = new Image();
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');

              if (!ctx) {
                reject(new Error('Canvas context not available'));
                return;
              }

              img.onload = () => {
                // リサイズ計算
                let { width, height } = img;
                const maxWidth = 1000;
                
                if (width > maxWidth) {
                  height = (height * maxWidth) / width;
                  width = maxWidth;
                }

                canvas.width = width;
                canvas.height = height;

                // 画像を描画
                ctx.drawImage(img, 0, 0, width, height);

                // 品質を調整しながら50KB以下にする
                const tryCompress = (quality: number): void => {
                  canvas.toBlob(
                    (blob) => {
                      if (!blob) {
                        reject(new Error('Failed to create blob'));
                        return;
                      }

                      const targetSize = 50 * 1024; // 50KB

                      if (blob.size <= targetSize || quality <= 0.1) {
                        resolve({
                          blob,
                          size: blob.size,
                          dimensions: { width, height }
                        });
                      } else {
                        // サイズがまだ大きい場合は品質を下げて再試行
                        const newQuality = Math.max(0.1, quality - 0.1);
                        setTimeout(() => tryCompress(newQuality), 10);
                      }
                    },
                    'image/jpeg',
                    quality
                  );
                };

                // 品質0.9から開始
                tryCompress(0.9);
              };

              img.onerror = () => {
                reject(new Error('画像の読み込みに失敗しました'));
              };

              img.src = URL.createObjectURL(file);
            });
            
            const optimizedPreviewUrl = URL.createObjectURL(result.blob);
            
            setOptimizedResult(result);
            setOptimizedPreview(optimizedPreviewUrl);
          } catch (err) {
            setError(err instanceof Error ? err.message : '処理中にエラーが発生しました');
          } finally {
            setIsProcessing(false);
          }
          
          // URLのクエリパラメータをクリア
          if (window.history.replaceState) {
            window.history.replaceState({}, document.title, window.location.pathname);
          }
        } catch (error) {
          setError('前のツールからの画像データの読み込みに失敗しました。');
          console.error('Failed to load image from URL:', error);
        }
      }
    };

    loadImageFromURL();
  }, []);

  // ファイルの検証
  const validateFile = (file: File): boolean => {
    if (!file.type.includes('jpeg') && !file.type.includes('jpg')) {
      setError('JPEGファイルのみ対応しています。');
      return false;
    }
    if (file.size > 10 * 1024 * 1024) { // 10MB制限
      setError('ファイルサイズが大きすぎます（10MB以下にしてください）。');
      return false;
    }
    return true;
  };

  // 画像のリサイズと圧縮
  const optimizeImage = useCallback(async (file: File): Promise<OptimizedResult> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('Canvas context not available'));
        return;
      }

      img.onload = () => {
        // リサイズ計算
        let { width, height } = img;
        const maxWidth = 1000;
        
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }

        canvas.width = width;
        canvas.height = height;

        // 画像を描画
        ctx.drawImage(img, 0, 0, width, height);

        // 品質を調整しながら50KB以下にする
        const tryCompress = (quality: number): void => {
          canvas.toBlob(
            (blob) => {
              if (!blob) {
                reject(new Error('Failed to create blob'));
                return;
              }

              const targetSize = 50 * 1024; // 50KB

              if (blob.size <= targetSize || quality <= 0.1) {
                resolve({
                  blob,
                  size: blob.size,
                  dimensions: { width, height }
                });
              } else {
                // サイズがまだ大きい場合は品質を下げて再試行
                const newQuality = Math.max(0.1, quality - 0.1);
                setTimeout(() => tryCompress(newQuality), 10);
              }
            },
            'image/jpeg',
            quality
          );
        };

        // 品質0.9から開始
        tryCompress(0.9);
      };

      img.onerror = () => {
        reject(new Error('画像の読み込みに失敗しました'));
      };

      img.src = URL.createObjectURL(file);
    });
  }, []);

  // ファイル処理
  const processFile = useCallback(async (file: File) => {
    if (!validateFile(file)) return;

    setError('');
    setIsProcessing(true);
    
    try {
      // プレビュー作成
      const originalPreviewUrl = URL.createObjectURL(file);
      setOriginalFile(file);
      setOriginalPreview(originalPreviewUrl);

      // 最適化処理
      const result = await optimizeImage(file);
      const optimizedPreviewUrl = URL.createObjectURL(result.blob);
      
      setOptimizedResult(result);
      setOptimizedPreview(optimizedPreviewUrl);
    } catch (err) {
      setError(err instanceof Error ? err.message : '処理中にエラーが発生しました');
    } finally {
      setIsProcessing(false);
    }
  }, [optimizeImage]);

  // ドラッグ&ドロップ処理
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      processFile(e.dataTransfer.files[0]);
    }
  }, [processFile]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  // ファイル選択処理
  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      processFile(e.target.files[0]);
    }
  }, [processFile]);

  // ダウンロード処理
  const handleDownload = useCallback(() => {
    if (!optimizedResult || !originalFile) return;

    const link = document.createElement('a');
    link.href = URL.createObjectURL(optimizedResult.blob);
    link.download = `optimized_${originalFile.name}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [optimizedResult, originalFile]);

  // 他のツールで続行
  const handleContinueWithTool = useCallback(async (toolId: string) => {
    if (!optimizedResult) return;

    setIsGeneratingURL(true);
    try {
      const url = await generateToolURL(toolId, optimizedResult.blob, 'jpeg-optimizer');
      window.location.href = url;
    } catch (error) {
      setError('ツール連携でエラーが発生しました。');
      console.error('Tool integration error:', error);
    } finally {
      setIsGeneratingURL(false);
    }
  }, [optimizedResult]);

  // リセット処理
  const handleReset = useCallback(() => {
    setOriginalFile(null);
    setOriginalPreview('');
    setOptimizedResult(null);
    setOptimizedPreview('');
    setError('');
    setSourceToolInfo(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  return (
    <div className="w-full mx-auto">
      {/* アップロードエリア */}
      <div
        className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${
          dragActive
            ? 'border-blue-400 bg-blue-400/10'
            : 'border-gray-600 hover:border-gray-500'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/jpg"
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <div className="text-6xl mb-4">📷</div>
        <h3 className="text-xl font-bold text-white mb-2">
          JPEGファイルをドロップまたはクリック
        </h3>
        <p className="text-gray-400 text-sm mb-4">
          最大幅1000px、50KB以下に自動最適化されます
        </p>
        <p className="text-gray-500 text-xs">
          対応形式: JPEG/JPG（最大10MB）
        </p>
      </div>

      {/* ソースツール情報表示 */}
      {sourceToolInfo && (
        <div className="mt-6 p-4 bg-green-900/50 border border-green-600 rounded-lg">
          <div className="text-center">
            <div className="text-2xl mb-2">✅</div>
            <p className="text-green-300 font-medium">{sourceToolInfo.tool}から画像を受け取りました</p>
            <p className="text-green-400 text-sm mt-1">{sourceToolInfo.message}</p>
          </div>
        </div>
      )}

      {/* エラー表示 */}
      {error && (
        <div className="mt-6 p-4 bg-red-900/50 border border-red-600 rounded-lg">
          <p className="text-red-300 text-sm text-center">{error}</p>
        </div>
      )}

      {/* 処理中表示 */}
      {isProcessing && (
        <div className="mt-6 p-8 bg-gray-800 rounded-lg text-center">
          <div className="text-4xl mb-4">⚙️</div>
          <p className="text-white font-medium">画像を最適化中...</p>
          <p className="text-gray-400 text-sm mt-2">しばらくお待ちください</p>
        </div>
      )}

      {/* 結果表示 */}
      {originalFile && optimizedResult && !isProcessing && (
        <div className="mt-8 space-y-6">
          {/* Before/After */}
          <div className="grid md:grid-cols-2 gap-6">
            {/* Before */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-bold text-white mb-4 text-center">
                変換前
              </h3>
              <div className="aspect-video bg-gray-700 rounded-lg mb-4 overflow-hidden">
                <img
                  src={originalPreview}
                  alt="Original"
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="text-center text-sm text-gray-400">
                <p>サイズ: {(originalFile.size / 1024).toFixed(1)}KB</p>
              </div>
            </div>

            {/* After */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-bold text-white mb-4 text-center">
                変換後
              </h3>
              <div className="aspect-video bg-gray-700 rounded-lg mb-4 overflow-hidden">
                <img
                  src={optimizedPreview}
                  alt="Optimized"
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="text-center text-sm text-gray-400">
                <p>サイズ: {(optimizedResult.size / 1024).toFixed(1)}KB</p>
                <p>
                  解像度: {optimizedResult.dimensions.width} × {optimizedResult.dimensions.height}
                </p>
              </div>
            </div>
          </div>

          {/* アクションボタン */}
          <div className="flex flex-col gap-4">
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <button
                onClick={handleDownload}
                disabled={isGeneratingURL}
                className="hero-cta-button inline-flex items-center disabled:opacity-50"
              >
                <span className="text-xl mr-2">💾</span>
                最適化画像をダウンロード
              </button>
              <button
                onClick={handleReset}
                disabled={isGeneratingURL}
                className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors disabled:opacity-50"
              >
                別の画像を選択
              </button>
            </div>
            
            {/* 他のツールとの連携ボタン */}
            {optimizedResult && availableTools.length > 0 && (
              <div className="border-t border-gray-600 pt-4">
                <p className="text-center text-gray-400 text-sm mb-3">他の画像処理ツールで続行</p>
                <div className="flex flex-wrap justify-center gap-2">
                  {availableTools.map((tool) => (
                    <button
                      key={tool.id}
                      onClick={() => handleContinueWithTool(tool.id)}
                      disabled={isGeneratingURL}
                      className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-500 transition-colors disabled:opacity-50 text-sm"
                    >
                      {isGeneratingURL ? '準備中...' : tool.title}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 圧縮率表示 */}
          <div className="text-center">
            <div className="inline-block bg-green-900/50 border border-green-600 rounded-lg px-6 py-3">
              <p className="text-green-300 font-medium">
                圧縮率: {(((originalFile.size - optimizedResult.size) / originalFile.size) * 100).toFixed(1)}% 削減
              </p>
              <p className="text-green-400 text-sm mt-1">
                {(originalFile.size / 1024).toFixed(1)}KB → {(optimizedResult.size / 1024).toFixed(1)}KB
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}