'use client';

import { useState, useCallback } from 'react';
import { diffWords, Change } from 'diff';

interface DiffResult {
  parts: Change[];
  hasChanges: boolean;
}

export default function TextDiffTool() {
  const [text1, setText1] = useState<string>('');
  const [text2, setText2] = useState<string>('');
  const [diffResult, setDiffResult] = useState<DiffResult | null>(null);
  const [isComparing, setIsComparing] = useState(false);

  // テキスト比較処理
  const compareTexts = useCallback(async () => {
    if (!text1.trim() && !text2.trim()) {
      setDiffResult({ parts: [], hasChanges: false });
      return;
    }

    setIsComparing(true);
    
    try {
      // 少し遅延を入れて処理中であることを示す
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const diff = diffWords(text1, text2);
      const hasChanges = diff.some(part => part.added || part.removed);
      
      setDiffResult({
        parts: diff,
        hasChanges
      });
    } catch (error) {
      console.error('比較処理でエラーが発生しました:', error);
      setDiffResult({ parts: [], hasChanges: false });
    } finally {
      setIsComparing(false);
    }
  }, [text1, text2]);

  // 差分結果をレンダリング
  const renderDiffResult = useCallback(() => {
    if (!diffResult) return null;

    if (!diffResult.hasChanges) {
      return (
        <div className="text-center py-8">
          <div className="text-2xl mb-2">✅</div>
          <p className="text-green-300 font-medium">テキストに違いはありません</p>
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {diffResult.parts.map((part, index) => {
          if (part.added) {
            return (
              <span
                key={index}
                className="bg-green-200/20 text-green-300 px-1 rounded"
                title="追加された部分"
              >
                {part.value}
              </span>
            );
          } else if (part.removed) {
            return (
              <span
                key={index}
                className="bg-red-200/20 text-red-300 px-1 rounded line-through"
                title="削除された部分"
              >
                {part.value}
              </span>
            );
          } else {
            return (
              <span key={index} className="text-gray-300">
                {part.value}
              </span>
            );
          }
        })}
      </div>
    );
  }, [diffResult]);

  // サンプルテキスト設定
  const setSampleTexts = useCallback(() => {
    setText1('今日は晴れていて、とても良い天気です。\n公園で散歩をしました。');
    setText2('今日は曇っていて、少し肌寒い天気です。\n図書館で読書をしました。');
  }, []);

  // リセット処理
  const handleReset = useCallback(() => {
    setText1('');
    setText2('');
    setDiffResult(null);
  }, []);

  return (
    <div className="w-full mx-auto">
      {/* 入力エリア */}
      <div className="grid md:grid-cols-2 gap-6 mb-8">
        {/* テキスト1 */}
        <div className="space-y-3">
          <h3 className="text-lg font-bold text-white flex items-center">
            <span className="text-blue-400 mr-2">A</span>
            比較元テキスト
          </h3>
          <textarea
            value={text1}
            onChange={(e) => setText1(e.target.value)}
            placeholder="比較したいテキストを入力してください..."
            className="w-full h-40 px-4 py-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:border-blue-500 transition-colors resize-none"
            disabled={isComparing}
          />
          <div className="text-xs text-gray-400">
            文字数: {text1.length}
          </div>
        </div>

        {/* テキスト2 */}
        <div className="space-y-3">
          <h3 className="text-lg font-bold text-white flex items-center">
            <span className="text-purple-400 mr-2">B</span>
            比較先テキスト
          </h3>
          <textarea
            value={text2}
            onChange={(e) => setText2(e.target.value)}
            placeholder="比較したいテキストを入力してください..."
            className="w-full h-40 px-4 py-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:border-purple-500 transition-colors resize-none"
            disabled={isComparing}
          />
          <div className="text-xs text-gray-400">
            文字数: {text2.length}
          </div>
        </div>
      </div>

      {/* コントロールボタン */}
      <div className="flex flex-wrap justify-center gap-4 mb-8">
        <button
          onClick={compareTexts}
          disabled={isComparing || (!text1.trim() && !text2.trim())}
          className="hero-cta-button inline-flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto"
        >
          <span className="text-xl mr-2">🔍</span>
          {isComparing ? '比較中...' : 'テキストを比較'}
        </button>
        <div className="flex gap-4 w-full sm:w-auto">
          <button
            onClick={setSampleTexts}
            disabled={isComparing}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-500 transition-colors disabled:opacity-50 flex-1 sm:flex-initial"
          >
            サンプル
          </button>
          <button
            onClick={handleReset}
            disabled={isComparing}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors disabled:opacity-50 flex-1 sm:flex-initial"
          >
            リセット
          </button>
        </div>
      </div>

      {/* 差分表示エリア */}
      {diffResult && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-bold text-white mb-4 text-center">
            比較結果
          </h3>
          
          {/* 凡例 */}
          <div className="flex justify-center gap-6 mb-6 text-sm">
            <div className="flex items-center">
              <span className="bg-green-200/20 text-green-300 px-2 py-1 rounded mr-2">追加</span>
            </div>
            <div className="flex items-center">
              <span className="bg-red-200/20 text-red-300 px-2 py-1 rounded line-through mr-2">削除</span>
            </div>
          </div>

          {/* 結果表示 */}
          <div className="bg-gray-900 rounded-lg p-4 min-h-32 text-sm leading-relaxed whitespace-pre-wrap font-mono">
            {renderDiffResult()}
          </div>
        </div>
      )}

      {/* 処理中表示 */}
      {isComparing && (
        <div className="text-center mt-4">
          <div className="inline-block bg-blue-900/50 border border-blue-600 rounded-lg px-6 py-3">
            <p className="text-blue-300 font-medium">テキストを比較中...</p>
          </div>
        </div>
      )}
    </div>
  );
}