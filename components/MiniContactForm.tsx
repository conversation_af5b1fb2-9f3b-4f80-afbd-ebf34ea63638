'use client';

import { useState, FormEvent } from 'react';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';
import { isRecaptchaEnabled } from '../utils/recaptchaUtils';

interface MiniContactFormProps {
  articleTitle: string;
  articleSlug: string;
}

interface FormData {
  email: string;
  inquiryType: string;
  website: string; // honeypot field
}

export default function MiniContactForm({ articleTitle, articleSlug }: MiniContactFormProps) {
  const { executeRecaptcha } = useGoogleReCaptcha();
  const [formData, setFormData] = useState<FormData>({
    email: '',
    inquiryType: 'article-question',
    website: '', // honeypot field
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');


  const inquiryOptions = [
    { value: 'article-question', label: '📄 記事について知りたい' },
    { value: 'consultation', label: '💬 相談したい' },
    { value: 'document-request', label: '📊 資料請求' },
    { value: 'meeting', label: '🤝 打ち合わせを設定したい' },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Check honeypot field (spam protection)
    if (formData.website) {
      return;
    }

    setIsSubmitting(true);
    setSubmitMessage('');

    try {
      // reCAPTCHA処理（環境変数で制御）
      let recaptchaToken = '';
      if (isRecaptchaEnabled() && executeRecaptcha) {
        try {
          recaptchaToken = await executeRecaptcha('mini_contact_form');
        } catch (recaptchaError) {
          console.warn('reCAPTCHA execution failed:', recaptchaError);
          // reCAPTCHAが失敗しても送信を続行
        }
      }
      
      const gasEndpoint = process.env.NEXT_PUBLIC_CONTACT_GAS_ENDPOINT;
      
      if (!gasEndpoint) {
        throw new Error('GAS endpoint not configured');
      }

      // お問い合わせ種別のラベルを取得
      const inquiryLabel = inquiryOptions.find(opt => opt.value === formData.inquiryType)?.label || formData.inquiryType;

      // フォームデータを送信用に整形
      const submissionData = {
        name: '記事からのお問い合わせ',
        company: '未記入',
        email: formData.email,
        phone: '未記入',
        message: `【記事からのお問い合わせ】\n\nお問い合わせ種別: ${inquiryLabel}\n記事タイトル: ${articleTitle}\n記事URL: https://www.space-cowboy.jp/articles/${articleSlug}`,
        timestamp: new Date().toISOString(),
        source: 'Article Mini Contact Form',
        ...(recaptchaToken && { recaptchaToken })
      };

      // フォーム送信
      await fetch(gasEndpoint, {
        method: 'POST',
        mode: 'no-cors',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData)
      });
      
      setSubmitMessage('送信完了しました！');
      
      // フォームをリセット
      setFormData({
        email: '',
        inquiryType: 'article-question',
        website: '',
      });
      
    } catch {
      setSubmitMessage('送信に失敗しました。');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* メールアドレス */}
      <div>
        <label htmlFor="mini-email" className="block text-xs font-medium mb-2 text-gray-300">
          メールアドレス *
        </label>
        <input
          type="email"
          id="mini-email"
          name="email"
          value={formData.email}
          onChange={handleInputChange}
          required
          placeholder="<EMAIL>"
          className="w-full px-3 py-2 bg-gray-700 text-white border border-gray-600 rounded focus:border-blue-400 outline-none transition-colors text-sm placeholder-gray-500"
        />
      </div>

      {/* お問い合わせ種別 */}
      <div>
        <label className="block text-xs font-medium mb-2 text-gray-300">
          お問い合わせ種別 *
        </label>
        <div className="space-y-2">
          {inquiryOptions.map((option) => (
            <label key={option.value} className="flex items-center cursor-pointer">
              <input
                type="radio"
                name="inquiryType"
                value={option.value}
                checked={formData.inquiryType === option.value}
                onChange={handleInputChange}
                className="w-3 h-3 text-blue-600 bg-gray-700 border-gray-600 focus:ring-0"
              />
              <span className="ml-2 text-xs text-gray-300">{option.label}</span>
            </label>
          ))}
        </div>
      </div>

      {/* ハニーポット（スパム対策） */}
      <div style={{ position: 'absolute', left: '-5000px' }} aria-hidden="true">
        <input
          type="text"
          name="website"
          value={formData.website}
          onChange={handleInputChange}
          tabIndex={-1}
          autoComplete="off"
        />
      </div>

      {/* 送信ボタン */}
      <button
        type="submit"
        disabled={isSubmitting}
        className="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isSubmitting ? '送信中...' : 'かんたん送信'}
      </button>

      {/* メッセージ */}
      {submitMessage && (
        <div className={`text-xs text-center ${submitMessage.includes('完了') ? 'text-green-400' : 'text-red-400'}`}>
          {submitMessage}
        </div>
      )}

      <p className="text-xs text-gray-500 text-center">
        詳細なご相談は<a href="/contact" className="text-blue-400 hover:underline">こちら</a>
      </p>

      {/* reCAPTCHA v3 表記 */}
      {isRecaptchaEnabled() && (
        <div className="recaptcha-terms">
          Protected by reCAPTCHA
        </div>
      )}
    </form>
  );
}