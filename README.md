# 株式会社スペースカウボーイ 公式ウェブサイト

## 🚀 概要

株式会社スペースカウボーイの公式ウェブサイトのソースコードです。Next.js 15を使用した静的サイトジェネレーション（SSG）で構築されています。

## 🛠 技術スタック

- **フレームワーク**: Next.js 15.5.3
- **言語**: TypeScript
- **スタイリング**: Tailwind CSS v4
- **コンテンツ管理**: MDX (記事管理)
- **デプロイ**: Xserverへの静的エクスポート
- **CI/CD**: GitHub Actions

## 📁 プロジェクト構造

```
spacecowboy_web/
├── app/                       # Next.js App Router
│   ├── articles/              # 記事ページ
│   │   ├── components/        # 記事専用コンポーネント
│   │   ├── [slug]/           # 動的記事ページ
│   │   └── page.tsx          # 記事一覧ページ
│   ├── games/                # ゲームコンテンツ
│   │   ├── eSupport/         # eSupportアンケートゲーム
│   │   │   ├── components/   # eSupport専用コンポーネント
│   │   │   └── thanks/       # サンクスページ
│   │   ├── pegasus-wing-fortune/  # ペガサスゲーム
│   │   └── page.tsx          # ゲーム一覧ページ
│   ├── contact/              # お問い合わせページ
│   ├── service/              # サービス紹介ページ
│   ├── company/              # 会社情報ページ
│   └── layout.tsx            # ルートレイアウト
├── components/               # 🔧 共通コンポーネント
│   ├── Navigation.tsx        # グローバルナビゲーション
│   ├── Footer.tsx           # フッター
│   ├── ContentCard.tsx      # コンテンツカード
│   ├── GameCard.tsx         # ゲームカード
│   ├── CTASection.tsx       # CTA セクション
│   └── mdx-components.tsx   # MDX設定
├── content/                 # MDXコンテンツ
│   └── articles/           # 記事のMDXファイル
├── lib/                    # ユーティリティ・ライブラリ
│   └── articles/          # 記事管理ロジック
├── public/                 # 静的アセット
│   ├── img/               # 画像ファイル
│   └── ...
├── api/                   # Cloud Run API (アンケート分析)
│   ├── survey_api.py     # Flask API
│   ├── Dockerfile        # コンテナ設定
│   └── deploy.sh         # デプロイスクリプト
└── archive/              # 旧静的サイトのアーカイブ
    ├── static-site/      # 旧HTML/CSS/JS
    └── wip/              # 開発資料
```

## 🚀 開発環境のセットアップ

### 必要な環境
- Node.js 20.x
- npm 9.x

### インストール
```bash
npm install
```

### 環境変数の設定
`.env.local`ファイルを作成し、以下の環境変数を設定：

```env
# 開発モード設定
NEXT_PUBLIC_DEV_MODE=true

# Google Apps Script エンドポイント
NEXT_PUBLIC_CONTACT_GAS_ENDPOINT=your_contact_endpoint
NEXT_PUBLIC_ESUPPORT_GAS_ENDPOINT=your_esupport_endpoint

# パスワード保護
NEXT_PUBLIC_PASSWORD=your_password

# Cloud Run API
NEXT_PUBLIC_CLOUD_RUN_API=your_api_endpoint

# Google Analytics
NEXT_PUBLIC_GA_ID=your_ga_id
```

### 開発サーバーの起動
```bash
npm run dev
```

[http://localhost:3000](http://localhost:3000) でアプリケーションが起動します。

## 📦 ビルドとデプロイ

### ローカルビルド
```bash
npm run build
```

### 静的ファイルの確認
```bash
npx serve out
```

### 本番デプロイ
mainブランチへのプッシュで自動的にGitHub Actionsが実行され、Xserverへデプロイされます。

## 🔐 パスワード保護機能

以下のコンテンツはパスワード保護されています：
- 記事: `quando_01_q`, `quando_01`, `quando_02`
- ゲーム: ステータスが「限定公開」のもの

## 📝 コンテンツ管理

### 記事の追加
1. `content/articles/`に`.mdx`ファイルを作成
2. フロントマターでメタデータを設定
3. MDX形式でコンテンツを記述

### ケーススタディの追加
`app/service/CaseStudiesSection.tsx`の`caseStudiesData`配列に追加

### ゲームの追加
`app/games/GamesContent.tsx`の`gamesData`配列に追加

## 🏗️ コンポーネント設計

### 共通コンポーネント (`/components/`)
- **Navigation.tsx**: グローバルナビゲーション（レスポンシブ対応）
- **Footer.tsx**: サイト共通フッター
- **ContentCard.tsx**: 記事・ケーススタディ表示用カード
- **GameCard.tsx**: ゲーム表示用カード（パスワード保護対応）
- **CTASection.tsx**: CTA（Call to Action）セクション
- **mdx-components.tsx**: MDX記事のコンポーネント設定

### 専用コンポーネント
- **記事専用** (`/app/articles/components/`): ShareButtons, ArticleHeader等
- **eSupport専用** (`/app/games/eSupport/components/`): AIAnalysis, ESupportGame等
- **ペガサス専用** (`/app/games/pegasus-wing-fortune/components/`): ゲーム関連コンポーネント

## 🌐 API連携

### Cloud Run API (アンケート分析)
- エンドポイント: `/api/survey-summary`
- 用途: eSupportアンケートの回答をAIで分析

### Google Apps Script
- お問い合わせフォーム送信
- eSupportアンケート結果の保存

## 🧪 テスト・開発

```bash
# ビルドテスト
npm run build

# 開発サーバー起動
npm run dev

# 静的ファイル確認
npx serve out

# TypeScriptの型チェック（存在する場合）
npm run type-check

# ESLintチェック
npm run lint
```

## 🚀 プロジェクト再構成履歴

このプロジェクトは2025年9月に大規模な再構成を行いました：

### Before (旧構造)
- `/spacecowboy-next/` - Next.jsプロジェクト
- `/static/` - 静的サイト
- ルートに各種HTMLファイル

### After (現在の構造) 
- Next.jsプロジェクトをルートに展開
- 静的サイトを `/archive/static-site/` に保存
- API関連を `/api/` に整理
- コンポーネント構造を最適化

## 📄 ライセンス

このプロジェクトは株式会社スペースカウボーイの専有ソフトウェアです。

## 📞 お問い合わせ

- ウェブサイト: [https://www.space-cowboy.jp](https://www.space-cowboy.jp)
- メール: <EMAIL>

---

© 2025 株式会社スペースカウボーイ All Rights Reserved.