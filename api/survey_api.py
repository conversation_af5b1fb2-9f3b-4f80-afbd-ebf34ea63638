#!/usr/bin/env python3
"""
従業員アンケート分析Web APIサーバー
"""

import os
from dotenv import load_dotenv
from flask import Flask, jsonify, request
from flask_cors import CORS
import json
import pandas as pd
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from openai import OpenAI
from collections import Counter
import traceback

# 環境変数を読み込み
load_dotenv()

app = Flask(__name__)
CORS(app)  # CORSを有効にしてフロントエンドからのアクセスを許可

def load_config():
    """環境変数からAPIキーとクレデンシャル情報を読み込む"""
    try:
        # OpenAI APIキーを取得
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OPENAI_API_KEY環境変数が設定されていません")
        
        # Google Service Account JSONを取得
        client_json_str = os.getenv('GOOGLE_CLIENT_JSON')
        if not client_json_str:
            raise ValueError("GOOGLE_CLIENT_JSON環境変数が設定されていません")
        
        client_json = json.loads(client_json_str)
        
        return api_key, client_json
    
    except Exception as e:
        raise Exception(f"環境変数の読み込みに失敗しました: {str(e)}")

def connect_to_google_sheets(client_json, spreadsheet_id):
    """Googleスプレッドシートに接続"""
    scope = ['https://spreadsheets.google.com/feeds',
            'https://www.googleapis.com/auth/drive']
    
    credentials = ServiceAccountCredentials.from_json_keyfile_dict(client_json, scope)
    gc = gspread.authorize(credentials)
    
    spreadsheet = gc.open_by_key(spreadsheet_id)
    return spreadsheet

def get_survey_data(spreadsheet, sheet_name='responses'):
    """アンケートデータを取得してDataFrameに変換"""
    sheet = spreadsheet.worksheet(sheet_name)
    data = sheet.get_all_records()
    
    if not data:
        raise ValueError("シートにデータがありません")
    
    df = pd.DataFrame(data)
    return df

def analyze_survey_data(df):
    """アンケートデータを分析してサマリーを作成"""
    analysis = {}
    
    # 各質問項目のカテゴリ
    question_columns = [
        'Optimized Timing',
        'Optimized Service', 
        'Optimized Communication',
        'Optimized Value',
        'No.1 Distributor'
    ]
    
    # 基本統計
    analysis['total_responses'] = len(df)
    analysis['departments'] = df['Department'].value_counts().to_dict()
    analysis['age_groups'] = df['Age'].value_counts().to_dict()
    analysis['gender_distribution'] = df['Gender'].value_counts().to_dict()
    
    # 各質問の回答を分析
    analysis['question_responses'] = {}
    for column in question_columns:
        if column in df.columns:
            # 空でない回答のみを取得
            responses = df[column].dropna().astype(str)
            responses = responses[responses.str.strip() != '']
            
            analysis['question_responses'][column] = {
                'count': len(responses),
                'sample_responses': responses.head(10).tolist(),  # 最初の10件をサンプルとして
            }
    
    return analysis

def generate_summary_with_openai(api_key, analysis):
    """OpenAI GPT-4を使って従業員意識の要約を生成"""
    try:
        # OpenAI クライアントを初期化（proxies引数なし、最新API準拠）
        client = OpenAI(api_key=api_key)
        
        # プロンプトを作成
        prompt = create_analysis_prompt(analysis)
        
        # GPT-4で要約生成
        response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {
                    "role": "system",
                    "content": "あなたは従業員アンケートの結果を分析する専門家です。データに基づいて、従業員の現在の意識や考えを客観的に要約してください。300文字程度で簡潔にまとめてください。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            max_tokens=500,
            temperature=0.7
        )
        
        return response.choices[0].message.content.strip()
        
    except Exception as e:
        # OpenAI APIエラーの詳細ログ
        error_msg = f"OpenAI API初期化またはリクエストエラー: {str(e)}"
        print(f"ERROR: {error_msg}")
        raise Exception(error_msg)

def create_analysis_prompt(analysis):
    """分析データからOpenAI用のプロンプトを作成"""
    prompt = f"""
以下は従業員アンケートの分析結果です。この結果をもとに、現在の従業員の意識やミッションに対する実感を300文字程度で要約してください。

【基本情報】
- 回答者数: {analysis['total_responses']}人
- 部署別分布: {analysis['departments']}
- 年齢層分布: {analysis['age_groups']}
- 性別分布: {analysis['gender_distribution']}

【質問別回答分析】
"""
    
    for question, data in analysis['question_responses'].items():
        prompt += f"""
■ {question}
- 回答数: {data['count']}件
- サンプル回答: {', '.join(data['sample_responses'][:3])}
"""
    
    prompt += """

【要約の観点】
1. 従業員が現在重視している価値観や考え方
2. ミッション・ビジョンに対する理解度や共感度
3. 組織への帰属意識や満足度
4. 今後の改善点や期待

上記の観点を踏まえ、データに基づいて客観的に従業員の意識を要約してください。
"""
    
    return prompt

@app.route('/api/survey-summary', methods=['GET'])
def get_survey_summary():
    """従業員アンケートのAI要約を取得するAPI"""
    try:
        # 設定情報を環境変数から取得
        spreadsheet_id = os.getenv('GOOGLE_SPREADSHEET_ID')
        sheet_name = os.getenv('GOOGLE_SHEET_NAME', 'responses')
        
        if not spreadsheet_id:
            raise ValueError("GOOGLE_SPREADSHEET_ID環境変数が設定されていません")
        
        # 1. 環境変数から情報を読み込み
        api_key, client_json = load_config()
        
        # 2. Googleスプレッドシートに接続
        spreadsheet = connect_to_google_sheets(client_json, spreadsheet_id)
        
        # 3. アンケートデータを取得
        df = get_survey_data(spreadsheet, sheet_name)
        
        # 4. データを分析
        analysis = analyze_survey_data(df)
        
        # 5. OpenAI GPT-4で要約生成
        summary = generate_summary_with_openai(api_key, analysis)
        
        # 6. レスポンスを返す
        return jsonify({
            'success': True,
            'data': {
                'summary': summary,
                'total_responses': analysis['total_responses'],
                'departments': analysis['departments'],
                'timestamp': pd.Timestamp.now().isoformat()
            }
        })
        
    except Exception as e:
        error_details = traceback.format_exc()
        return jsonify({
            'success': False,
            'error': str(e),
            'details': error_details
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """ヘルスチェック用エンドポイント"""
    return jsonify({
        'success': True,
        'message': 'Survey Analysis API is running',
        'timestamp': pd.Timestamp.now().isoformat()
    })

if __name__ == '__main__':
    port = int(os.getenv('FLASK_PORT', 8080))
    debug = os.getenv('FLASK_ENV') != 'production'
    
    print("🚀 従業員アンケート分析APIサーバーを起動します...")
    print("📡 エンドポイント:")
    print(f"  - http://localhost:{port}/api/survey-summary (AI要約取得)")
    print(f"  - http://localhost:{port}/api/health (ヘルスチェック)")
    print(f"\n✨ サーバー起動中... (Port: {port}, Debug: {debug})")
    
    app.run(debug=debug, host='0.0.0.0', port=port)