#!/bin/bash

# Cloud Runデプロイスクリプト
# 使用方法: ./deploy.sh [PROJECT_ID]

# プロジェクトIDを設定
PROJECT_ID=${1:-"esupport-471802"}
SERVICE_NAME="survey-api"
REGION="asia-northeast1"

echo "🚀 Google Cloud Runへのデプロイを開始します..."
echo "📋 プロジェクトID: $PROJECT_ID"
echo "🌏 リージョン: $REGION"
echo "🔧 サービス名: $SERVICE_NAME"

# Google Cloudプロジェクトを設定
echo "📝 Google Cloudプロジェクトを設定中..."
gcloud config set project $PROJECT_ID

# Docker イメージをビルド
echo "🔨 Dockerイメージをビルド中..."
gcloud builds submit --tag gcr.io/$PROJECT_ID/$SERVICE_NAME

# Cloud Runにデプロイ
echo "☁️ Cloud Runにデプロイ中..."
gcloud run deploy $SERVICE_NAME \
  --image gcr.io/$PROJECT_ID/$SERVICE_NAME \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --memory 1Gi \
  --cpu 1 \
  --concurrency 80 \
  --max-instances 10 \
  --set-env-vars FLASK_ENV=production

# デプロイ結果を取得
echo "📋 デプロイ結果を取得中..."
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --platform managed --region $REGION --format 'value(status.url)')

echo ""
echo "✅ デプロイ完了!"
echo "🌐 サービスURL: $SERVICE_URL"
echo "🔗 API エンドポイント:"
echo "   - $SERVICE_URL/api/survey-summary"
echo "   - $SERVICE_URL/api/health"
echo ""
echo "📝 次のステップ:"
echo "1. フロントエンドのAPIエンドポイントを以下に変更してください:"
echo "   fetch('$SERVICE_URL/api/survey-summary')"
echo "2. 環境変数を Cloud Run コンソールで設定してください:"
echo "   - OPENAI_API_KEY"
echo "   - GOOGLE_CLIENT_JSON"
echo "   - GOOGLE_SPREADSHEET_ID"
echo "   - GOOGLE_SHEET_NAME"
echo ""