/** @type {import('next').NextConfig} */
const nextConfig = {
  // 静的エクスポート設定
  output: 'export',
  trailingSlash: true,
  
  // 画像最適化を無効化（静的エクスポート用）
  images: {
    unoptimized: true,
  },
  
  // CSS最適化設定（実験的機能を無効化）
  experimental: {
    // optimizeCss: true, // critters依存関係エラーのため無効化
    optimizePackageImports: ['react', 'react-dom'],
  },
  
  // CSS分割とキャッシュ最適化
  onDemandEntries: {
    // キャッシュ期間を延長（秒）
    maxInactiveAge: 60 * 1000,
    // メモリ使用量を制限（ページ数）
    pagesBufferLength: 5,
  },
  
  // 画像最適化（静的エクスポートでは無効）
  // images: {
  //   formats: ['image/webp', 'image/avif'],
  //   minimumCacheTTL: 3600,
  // },
  
  // バンドル分析用設定（開発時）
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // CSS最適化
    if (!dev && !isServer) {
      // CSS分割設定
      config.optimization.splitChunks.cacheGroups.styles = {
        name: 'styles',
        test: /\.(css|scss|sass)$/,
        chunks: 'all',
        enforce: true,
      };
    }
    
    // Bundle Analyzer（本番ビルド時のみ）
    if (!dev && process.env.ANALYZE === 'true') {
      const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          openAnalyzer: false,
          reportFilename: 'bundle-report.html',
        })
      );
    }
    
    return config;
  },
  
  // ヘッダー設定でキャッシュ最適化（静的エクスポート時は無効）
  // async headers() {
  //   return [
  //     {
  //       source: '/static/(.*)',
  //       headers: [
  //         {
  //           key: 'Cache-Control',
  //           value: 'public, max-age=31536000, immutable',
  //         },
  //       ],
  //     },
  //     {
  //       source: '/(.*).css',
  //       headers: [
  //         {
  //           key: 'Cache-Control',
  //           value: 'public, max-age=31536000, immutable',
  //         },
  //       ],
  //     },
  //   ];
  // },
  
  // MDX設定（既存の設定があれば統合）
  pageExtensions: ['js', 'jsx', 'mdx', 'md', 'ts', 'tsx'],
};

module.exports = nextConfig;