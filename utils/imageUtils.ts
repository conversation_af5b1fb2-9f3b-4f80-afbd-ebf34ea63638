// 画像処理用ユーティリティ関数

/**
 * BlobをBase64文字列に変換
 */
export const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      if (typeof reader.result === 'string') {
        // data:image/png;base64, の部分を除去してBase64文字列のみを返す
        const base64 = reader.result.split(',')[1];
        resolve(base64);
      } else {
        reject(new Error('Failed to convert blob to base64'));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read blob'));
    reader.readAsDataURL(blob);
  });
};

/**
 * Base64文字列をBlobに変換
 */
export const base64ToBlob = (base64: string, mimeType: string = 'image/png'): Blob => {
  try {
    // Base64文字列をバイナリデータに変換
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  } catch (error) {
    throw new Error('Failed to convert base64 to blob');
  }
};

/**
 * BlobからFileオブジェクトを作成
 */
export const blobToFile = (blob: Blob, fileName: string): File => {
  return new File([blob], fileName, { type: blob.type });
};

/**
 * LocalStorageから画像データを取得
 */
export const getImageFromURL = (): { data: string; source: string } | null => {
  if (typeof window === 'undefined') return null;
  
  // URLパラメータでツール間連携が指定されているか確認
  const params = new URLSearchParams(window.location.search);
  const fromTool = params.get('from');
  
  if (fromTool) {
    // LocalStorageからデータを取得
    const storageKey = `tool-integration-${fromTool}`;
    const storedData = localStorage.getItem(storageKey);
    
    if (storedData) {
      try {
        const parsedData = JSON.parse(storedData);
        // データを使用後にクリア
        localStorage.removeItem(storageKey);
        return { data: parsedData.data, source: parsedData.source };
      } catch (error) {
        console.error('Failed to parse stored image data:', error);
        localStorage.removeItem(storageKey);
      }
    }
  }
  
  return null;
};

/**
 * ツール間連携用のURLを生成（LocalStorage使用）
 */
export const generateToolURL = async (
  targetTool: string, 
  imageBlob: Blob, 
  sourceTool: string
): Promise<string> => {
  try {
    console.log('generateToolURL called with:', { targetTool, sourceTool, blobSize: imageBlob.size });
    const base64Data = await blobToBase64(imageBlob);
    console.log('Base64 data length:', base64Data.length);
    
    // LocalStorageにデータを保存
    const storageKey = `tool-integration-${sourceTool}`;
    const dataToStore = {
      data: base64Data,
      source: sourceTool,
      timestamp: Date.now()
    };
    
    localStorage.setItem(storageKey, JSON.stringify(dataToStore));
    console.log('Image data stored in localStorage with key:', storageKey);
    
    // URLパラメータにはソースツール名のみを指定
    const url = `/games/${targetTool}/?from=${sourceTool}`;
    console.log('Final URL generated:', url);
    return url;
  } catch (error) {
    console.error('generateToolURL error:', error);
    throw new Error('Failed to generate tool URL');
  }
};

/**
 * 画像ファイルのMIMEタイプを取得
 */
export const getImageMimeType = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  
  switch (extension) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'webp':
      return 'image/webp';
    default:
      return 'image/png'; // デフォルト
  }
};

/**
 * 現在のツール以外の画像処理ツールを取得
 */
export const getAvailableImageTools = (currentToolId: string): Array<{id: string, title: string, url: string}> => {
  const imageProcessingTools = [
    { id: 'square-cropper', title: '正方形クロップツール', url: '/games/square-cropper' },
    { id: 'image-resizer', title: '画像リサイズツール', url: '/games/image-resizer' },
    { id: 'jpeg-optimizer', title: 'JPEG最適化ツール', url: '/games/jpeg-optimizer' },
    { id: 'webp-optimizer', title: 'WebP変換ツール', url: '/games/webp-optimizer' }
  ];
  
  return imageProcessingTools.filter(tool => tool.id !== currentToolId);
};