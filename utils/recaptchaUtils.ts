// reCAPTCHA関連のユーティリティ関数

/**
 * reCAPTCHAが有効かどうかを判定
 * @returns {boolean} reCAPTCHAが有効な場合はtrue
 */
export const isRecaptchaEnabled = (): boolean => {
  return process.env.NEXT_PUBLIC_RECAPTCHA_ENABLED === 'true';
};

/**
 * reCAPTCHAサイトキーを取得
 * @returns {string | undefined} サイトキー（設定されていない場合はundefined）
 */
export const getRecaptchaSiteKey = (): string | undefined => {
  return process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;
};

/**
 * reCAPTCHAが適切に設定されているかを確認
 * @returns {boolean} reCAPTCHAが有効かつサイトキーが設定されている場合はtrue
 */
export const isRecaptchaConfigured = (): boolean => {
  return isRecaptchaEnabled() && !!getRecaptchaSiteKey();
};