import { Metadata } from 'next';
import Navigation from '../../components/Navigation';
import Footer from '../../components/Footer';
import CTASection from '../../components/CTASection';
import Image from 'next/image';

export const metadata: Metadata = {
  title: '会社概要 | 株式会社スペースカウボーイ',
  description: '福岡を拠点に宇宙から地上まで表現と技術で課題解決を行う株式会社スペースカウボーイの会社概要・沿革・アクセス情報です。',
  openGraph: {
    title: '会社概要 | 株式会社スペースカウボーイ',
    description: '福岡を拠点に宇宙から地上まで表現と技術で課題解決を行う株式会社スペースカウボーイの会社概要・沿革・アクセス情報です。',
    url: 'https://www.space-cowboy.jp/company?v=2',
  },
};

export default function CompanyPage() {
  return (
    <main className="bg-gray-900/0 text-white company-page">
      <Navigation />
      
      {/* パララックス背景 */}
      <div className="parallax-bg" id="parallax-bg"></div>
      
      {/* 背景を暗くするオーバーレイ */}
      <div className="fixed inset-0 bg-black/30 z-0"></div>

      {/* メインコンテンツ */}
      <div className="parallax-wrapper">
        <section className="min-h-screen pt-32 pb-0 md:pt-48 md:pb-0 relative z-10">
          <div className="max-w-6xl mx-auto px-6 md:px-12">
            {/* ページタイトル */}
            <div className="mb-56 animate-fadeInUpSlow">
              <h1 className="text-5xl md:text-7xl montserrat-bold text-white mb-8">COMPANY</h1>
              <p className="text-xl md:text-2xl text-blue-300 font-light mb-8">
                宇宙とクリエイティブの交差点で、新しい価値を
              </p>
            </div>
          </div>

          {/* 会社情報エリア */}
          <div className="w-full bg-gray-900/80">
            <div className="max-w-6xl mx-auto px-6 md:px-12 py-24 space-y-32">

              {/* ビジョン */}
              <div id="vision" className="scroll-mt-32">
                <div className="border-l-4 border-gray-300 pl-4 mb-12">
                  <p className="text-sm text-gray-300 tracking-widest mb-1">01 — ビジョン</p>
                  <h3 className="text-3xl md:text-4xl montserrat-bold tracking-wide">VISION</h3>
                </div>
                <div>
                  <h4 className="text-3xl font-bold text-white mb-3">宇宙を身近に、クリエイティブを自由に。</h4>
                  <p className="text-gray-300 leading-loose">
                    私たちは、宇宙産業とクリエイティブの架け橋となり、<br />
                    誰もが宇宙の可能性に触れられる世界を創造します。
                  </p>
                </div>
              </div>

              {/* ミッション */}
              <div id="mission" className="scroll-mt-32">
                <div className="border-l-4 border-gray-300 pl-4 mb-12">
                  <p className="text-sm text-gray-300 tracking-widest mb-1">02 — ミッション</p>
                  <h3 className="text-3xl md:text-4xl montserrat-bold tracking-wide">MISSION</h3>
                </div>
                <div>
                  <h4 className="text-3xl font-bold text-white mb-3">技術と表現の融合により、宇宙産業の発展に貢献。</h4>
                  <p className="text-gray-300 leading-loose">
                    新しい視点とアプローチで、宇宙ビジネスの<br />
                    社会実装を加速させます。<br />
                    誰もが宇宙の可能性に触れられる世界を創造します。
                  </p>
                </div>
              </div>

              {/* 代表メッセージ */}
              <div id="ceo-message" className="scroll-mt-32">
                <div className="border-l-4 border-gray-300 pl-4 mb-12">
                  <p className="text-sm text-gray-300 tracking-widest mb-1">03 — 代表メッセージ</p>
                  <h3 className="text-3xl md:text-4xl montserrat-bold tracking-wide">MESSAGE</h3>
                </div>

                <div className="md:flex md:gap-12 items-center">
                  <div className="md:w-2/3 mb-8 md:mb-0 md:order-1">
                    <p className="text-2xl font-bold text-gray-300 leading-loose">
                      見上げよう空を、つながろう宇宙で。
                    </p>
                    <br />
                    <p className="text-sm text-gray-300 leading-loose mb-6">
                      「宇宙」と聞いて、みなさんは何を思い浮かべるでしょうか。<br />
                      遠い未来の話？ 限られた人だけの世界？
                    </p>
                    <p className="text-sm text-gray-300 leading-loose mb-6">
                      私たちスペースカウボーイは、そんな固定観念を打ち破り、<br />
                      宇宙をもっと身近で、楽しく、ワクワクする存在にしたいと考えています。
                    </p>
                    <p className="text-sm text-gray-300 leading-loose mb-6">
                      映像、デザイン、テクノロジー。<br />
                      そのすべてを融合し、宇宙産業に新しい風を吹き込みます。
                    </p>
                    <p className="text-sm text-gray-300 leading-loose mb-12">
                      私たちの挑戦は、まだ始まったばかりです。<br />
                      人々が空を見上げたとき、そこに&quot;あなたの宇宙&quot;が感じられるように。<br />
                      スペースカウボーイは、誰もが参加できる宇宙時代の入口をつくっていきます。
                    </p>

                    <p className="text-base text-gray-300 leading-loose mb-6">
                      代表取締役 CEO 香月 浩一
                    </p>
                  </div>

                  <div className="md:w-1/3 md:order-2">
                    {/* デスクトップ用：大きい写真 */}
                    <Image
                      src="/img/ceo_profile.jpg"
                      alt="代表取締役 香月 浩一"
                      width={300}
                      height={400}
                      className="hidden md:block w-full opacity-100 transition-opacity duration-1000 mb-4"
                    />

                    {/* スマホ用：写真と経歴を横並び */}
                    <div className="flex gap-4 md:hidden">
                      {/* スマホ用の小さい写真 */}
                      <Image
                        src="/img/ceo_profile.jpg"
                        alt="代表取締役 香月 浩一"
                        width={100}
                        height={120}
                        className="w-1/3 h-auto opacity-100 transition-opacity duration-1000 flex-shrink-0 object-cover"
                      />

                      {/* 経歴テキスト */}
                      <p className="text-xs text-gray-400 leading-relaxed flex-1">
                        1980年福岡出身。サンライズでガンダムなどのアニメ制作に携わった後、
                        映像ディレクターとしてTV、CM、自治体や企業のVPを制作し、文化庁メディア芸術祭で新人賞を受賞。
                        福岡放送での宣伝・SNS運用やウェブアプリ開発を経て、2024年に独立。
                        応用情報技術者(経済産業省認定)。上級ウェブ解析士(2020)。慶應義塾大卒。
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 pt-6 border-t border-gray-700/50">
              </div>

              {/* 会社概要 */}
              <div id="company-info" className="scroll-mt-32">
                <div className="border-l-4 border-gray-300 pl-4 mb-12">
                  <p className="text-sm text-gray-300 tracking-widest mb-1">04 — 会社概要</p>
                  <h3 className="text-3xl md:text-4xl montserrat-bold tracking-wide">COMPANY</h3>
                </div>

                <div className="text-sm text-white">
                  <table className="w-full border-separate border-spacing-x-4 border-spacing-y-0">
                    <tbody>
                      <tr>
                        <th className="text-left font-normal pl-4 pr-0 py-3 w-1/4 text-gray-400 border-b-2 border-gray-500">
                          会社名
                        </th>
                        <td className="pl-0 pr-4 py-3 text-white border-b border-gray-700">
                          株式会社スペースカウボーイ
                        </td>
                      </tr>
                      <tr>
                        <th className="text-left font-normal pl-4 pr-0 py-3 text-gray-400 border-b-2 border-gray-500">
                          設立
                        </th>
                        <td className="pl-0 pr-4 py-3 text-white border-b border-gray-700">2025年1月</td>
                      </tr>
                      <tr>
                        <th className="text-left font-normal pl-4 pr-0 py-3 text-gray-400 border-b-2 border-gray-500">
                          代表取締役
                        </th>
                        <td className="pl-0 pr-4 py-3 text-white border-b border-gray-700">香月 浩一</td>
                      </tr>
                      <tr>
                        <th className="text-left font-normal pl-4 pr-0 py-3 text-gray-400 border-b-2 border-gray-500">
                          資本金
                        </th>
                        <td className="pl-0 pr-4 py-3 text-white border-b border-gray-700">1,000,000円</td>
                      </tr>
                      <tr>
                        <th className="text-left font-normal pl-4 pr-0 py-3 text-gray-400 border-b-2 border-gray-500 align-top">
                          所在地
                        </th>
                        <td className="pl-0 pr-4 py-3 text-white border-b border-gray-700">
                          〒810-0001<br />
                          福岡市中央区天神4-6-28 天神ファーストビル7F
                        </td>
                      </tr>
                      <tr>
                        <th className="text-left font-normal pl-4 pr-0 py-3 text-gray-400 border-b-2 border-gray-500 align-top">
                          事業内容
                        </th>
                        <td className="pl-0 pr-4 py-3 text-white border-b border-gray-700">
                          ・宇宙関連コンテンツの企画・制作<br />
                          ・映像・Web・アプリケーション制作<br />
                          ・クリエイティブトータル支援
                        </td>
                      </tr>
                      <tr>
                        <th className="text-left font-normal pl-4 pr-0 py-3 text-gray-400 align-top">
                          アクセス
                        </th>
                        <td className="pl-0 pr-4 py-3 text-white">
                          地下鉄天神駅より徒歩5分<br />
                          西鉄福岡（天神）駅より徒歩7分
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

        </section>
      </div>

      <CTASection />
      <Footer />
    </main>
  );
}