import ContentCard from '../../components/ContentCard';

const caseStudiesData = [
  {
    id: 'case-08',
    date: '2025/8',
    division: 'クリエイティブ事業部',
    title: 'おみくじミニアプリ「ペガサス羽みくじ」作成',
    tags: ['ミニアプリ制作', 'クリエイティブデザイン'],
    image: '/img/case08.webp',
    link: {
      url: 'https://www.space-cowboy.jp/games/pegasus-wing-fortune',
      text: '詳しく見る',
      external: true
    }
  },
  {
    id: 'case-07',
    date: '2025/7',
    division: '宇宙事業部・クリエイティブ事業部',
    title: 'ISTS徳島で宇宙スタートアップなどの取材を実施',
    tags: ['クリエイティブトータル支援', '映像・動画制作'],
    image: '/img/case07.webp'
  },
  {
    id: 'case-06',
    date: '2025/6',
    division: '宇宙事業部',
    title: '株式会社minsora様の企業広告を制作',
    tags: ['クリエイティブトータル支援'],
    image: '/img/case06.webp'
  },
  {
    id: 'case-05',
    date: '2025',
    division: '宇宙事業部',
    title: '占いアプリ「Space Oracle」プロトタイプ設計',
    tags: ['Web/アプリ制作', 'UI設計・LP制作'],
    image: '/img/case05.webp'
  },
  {
    id: 'case-04',
    date: '2025/4/26',
    division: '宇宙事業部',
    title: '産学連携の宇宙ビジネス交流会を開催',
    tags: ['ポスター', 'セミナー・イベント企画'],
    image: '/img/case04.webp'
  },
  {
    id: 'case-03',
    date: '2025',
    division: '宇宙事業部',
    title: '衛星データを活用した作物育成管理アプリを開発支援',
    tags: ['Vue.js', 'Web/アプリ制作'],
    image: '/img/case03.webp'
  },
  {
    id: 'case-02',
    date: '2025',
    division: 'クリエイティブ事業部',
    title: '建築系Saas企業のM&Aセレモニーをドキュメント映像として制作',
    tags: ['映像・動画制作'],
    image: '/img/case02.webp'
  },
  {
    id: 'case-01',
    date: '2024',
    division: '宇宙事業部',
    title: '一般社団法人の広報業務をワンストップで受託',
    tags: ['クリエイティブトータル支援', '映像・動画制作', 'Webアプリ開発'],
    image: '/img/case01.webp'
  }
];

export default function CaseStudiesSection() {
  return (
    <div className="w-full bg-gray-900/80">
      <div className="max-w-6xl mx-auto px-6 md:px-12 py-24 space-y-32">
        <div id="case" className="scroll-mt-32">
          {/* セクションヘッダー */}
          <div className="border-l-4 border-gray-300 pl-4 mb-16">
            <p className="text-sm text-gray-300 tracking-widest mb-1">03 — 実績</p>
            <h3 className="text-3xl md:text-4xl montserrat-bold tracking-wide">CASE</h3>
          </div>

          {/* ケーススタディグリッド */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-8">
            {caseStudiesData.map((caseStudy) => (
              <ContentCard
                key={caseStudy.id}
                id={caseStudy.id}
                date={caseStudy.date}
                division={caseStudy.division}
                title={caseStudy.title}
                tags={caseStudy.tags}
                image={caseStudy.image}
                link={caseStudy.link}
                type="case"
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}