import Link from 'next/link';

interface FlowStep {
  step: string;
  title: string;
  description: string;
  link?: {
    url: string;
    text: string;
    internal: boolean;
  };
}

const flowSteps: FlowStep[] = [
  {
    step: 'STEP 01',
    title: 'お問い合わせ',
    description: 'お問い合わせフォームから、ご要望をお送りください。',
    link: {
      url: '/contact',
      text: 'お問い合わせ',
      internal: true
    }
  },
  {
    step: 'STEP 02',
    title: '初回無料ヒヤリング',
    description: '事業課題やプロジェクトのミッションについて、詳しくお聞かせください。'
  },
  {
    step: 'STEP 03',
    title: 'ご提案・ディスカッション',
    description: '競合分析・市場調査を行い、解決策をご提案いたします。'
  },
  {
    step: 'STEP 04',
    title: 'ご契約',
    description: 'ご提案内容にご納得いただけましたら、契約を締結いたします。'
  },
  {
    step: 'STEP 05',
    title: 'プロジェクト開始',
    description: '合意した計画に基づいて、プロジェクトを開始いたします。'
  }
];

export default function ServiceFlowSection() {
  return (
    <div className="w-full bg-gray-900/70">
      <div className="max-w-6xl mx-auto px-6 md:px-12 py-24">
        <div id="service-flow" className="scroll-mt-32">
          {/* セクションヘッダー */}
          <div className="border-l-4 border-gray-300 pl-4 mb-6">
            <p className="text-sm text-gray-300 tracking-widest mb-1">04 — サービスの流れ</p>
            <h3 className="text-3xl md:text-4xl text-gray-300 montserrat-bold tracking-wide">FLOW</h3>
          </div>

          {/* フローステップ */}
          <div className="space-y-4">
            {flowSteps.map((step, index) => (
              <div key={index}>
                {/* ステップカード */}
                <div className="border border-gray-700 p-6 md:flex md:items-start md:justify-between">
                  <div className="flex items-start space-x-6">
                    {/* ステップ番号 */}
                    <div className="text-center">
                      <p className="text-xs text-gray-400 montserrat-regular">STEP</p>
                      <p className="text-3xl font-bold text-white">{String(index + 1).padStart(2, '0')}</p>
                    </div>
                    
                    {/* 縦線 */}
                    <div className="h-full">
                      <div className="w-px h-12 bg-gray-500 my-2"></div>
                    </div>
                    
                    {/* コンテンツ */}
                    <div>
                      <h4 className="text-lg font-bold text-white mb-2">{step.title}</h4>
                      <p className="text-sm text-gray-300 mb-4">
                        {step.description}
                      </p>
                      
                      {step.link && step.link.internal && (
                        <Link 
                          href={step.link.url}
                          className="inline-block bg-white text-black text-sm font-bold px-5 py-2 rounded hover:bg-gray-200 transition"
                        >
                          {step.link.text}
                        </Link>
                      )}
                    </div>
                  </div>
                </div>

                {/* 下矢印（最後以外） */}
                {index < flowSteps.length - 1 && (
                  <div className="flex justify-center">
                    <svg className="w-6 h-6 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 15a.75.75 0 01-.53-.22l-4.5-4.5a.75.75 0 111.06-1.06L10 12.69l3.97-3.97a.75.75 0 011.06 1.06l-4.5 4.5a.75.75 0 01-.53.22z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}