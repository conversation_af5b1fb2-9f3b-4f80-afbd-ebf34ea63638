interface FAQItem {
  id: string;
  question: string;
  answer: string;
}

const faqData: FAQItem[] = [
  {
    id: 'faq-01',
    question: 'どのような業種・規模の企業様とお取引がありますか？',
    answer: '宇宙関連企業から一般企業まで、業種・規模を問わず幅広くご支援させていただいております。スタートアップから上場企業まで、お客様のフェーズに合わせた最適なソリューションをご提案いたします。'
  },
  {
    id: 'faq-02', 
    question: '料金体系はどのようになっていますか？',
    answer: 'プロジェクトの内容・規模により異なりますが、初回ヒヤリングは無料で承っております。お客様のご予算に応じて最適なプランをご提案させていただきますので、まずはお気軽にご相談ください。'
  },
  {
    id: 'faq-03',
    question: '対応可能な地域はどちらですか？', 
    answer: '福岡を拠点としておりますが、全国対応が可能です。オンラインでのお打ち合わせやプロジェクト進行にも対応しており、遠方のお客様ともスムーズに連携させていただいております。'
  },
  {
    id: 'faq-04',
    question: 'プロジェクトの期間はどのくらいかかりますか？',
    answer: 'プロジェクトの内容により異なりますが、簡単な制作物であれば1〜2週間、複雑なWebアプリケーション開発では数ヶ月を要する場合があります。初回ヒヤリングでスケジュールについても詳しくご説明いたします。'
  },
  {
    id: 'faq-05',
    question: '宇宙事業に詳しくない企業でも相談できますか？',
    answer: 'もちろんです。宇宙事業への参入をお考えの企業様や、宇宙をテーマにしたプロモーションをお考えの企業様にも、基礎から丁寧にご説明し、最適なソリューションをご提案いたします。'
  }
];

export default function FAQSection() {
  return (
    <div className="w-full bg-gray-900/80">
      <div className="max-w-6xl mx-auto px-6 md:px-12 py-24 space-y-12">
        <div id="faq" className="scroll-mt-32">
          {/* セクションヘッダー */}
          <div className="border-l-4 border-gray-300 pl-4 mb-6">
            <p className="text-sm text-gray-300 tracking-widest mb-1">05 — よくある質問</p>
            <h3 className="text-3xl md:text-4xl text-gray-300 montserrat-bold tracking-wide">FAQ</h3>
          </div>

          {/* FAQ一覧 */}
          <div className="space-y-6">
            {faqData.map((faq) => (
              <div key={faq.id} className="border border-gray-700 p-6 space-y-6">
                {/* 質問 */}
                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <p className="flex-shrink-0 w-12 h-12 rounded-full bg-white text-black font-bold text-xl flex items-center justify-center leading-none">
                      Q
                    </p>
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-white">
                      {faq.question}
                    </h4>
                  </div>
                </div>

                {/* 区切り線 */}
                <div className="px-6">
                  <hr className="border-t border-gray-700" />
                </div>

                {/* 回答 */}
                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <p className="flex-shrink-0 w-12 h-12 rounded-full bg-gray-500 text-white font-bold text-xl flex items-center justify-center leading-none">
                      A
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-regular text-white">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}