import { Metadata } from 'next';
import Navigation from '../../components/Navigation';
import Footer from '../../components/Footer';
import CTASection from '../../components/CTASection';
import CaseStudiesSection from './CaseStudiesSection';
import ServiceFlowSection from './ServiceFlowSection';
import FAQSection from './FAQSection';

export const metadata: Metadata = {
  title: 'サービス | 株式会社スペースカウボーイ',
  description: '宇宙から地上まで、表現と技術で課題解決。宇宙事業部とクリエイティブ事業部の2つの軸で企画・制作・運用をワンストップで支援します。',
  openGraph: {
    title: 'サービス | 株式会社スペースカウボーイ',
    description: '宇宙から地上まで、表現と技術で課題解決。宇宙事業部とクリエイティブ事業部の2つの軸で企画・制作・運用をワンストップで支援します。',
    url: 'https://www.space-cowboy.jp/service?v=2',
  },
};

export default function ServicePage() {
  return (
    <main className="bg-gray-900/0 text-white service-page">
      <Navigation />
      
      {/* パララックス背景 */}
      <div className="parallax-bg" id="parallax-bg"></div>
      
      {/* 背景を暗くするオーバーレイ */}
      <div className="fixed inset-0 bg-black/30 z-0"></div>

      {/* メインコンテンツ */}
      <div className="parallax-wrapper">
        <section className="min-h-screen pt-32 md:pt-48 relative z-10">
          <div className="max-w-6xl mx-auto px-6 md:px-12">
            {/* ページタイトル */}
            <div className="mb-48 animate-fadeInUpSlow">
              <h1 className="text-5xl md:text-7xl montserrat-bold text-white mb-8">SERVICE</h1>
              <p className="text-xl md:text-2xl text-blue-300 font-light mb-8">
                宇宙から地上まで、表現と技術で課題解決
              </p>
              <p className="text-base md:text-lg text-gray-300 leading-loose max-w-4xl">
                スペースカウボーイは「宇宙事業部」と「クリエイティブ事業部」の2つの軸で、
                宇宙に関する社会実装から地上での表現課題まで、企画・制作・運用をワンストップで支援します。
              </p>
            </div>
          </div>
          
          {/* コンテンツエリア */}
          <div className="w-full bg-gray-900/80">
            <div className="max-w-6xl mx-auto px-6 md:px-12 py-24 space-y-32">
              {/* 宇宙事業部 */}
              <div id="space-division" className="scroll-mt-32">
                <div className="mb-8">
                  <p className="text-4xl md:text-6xl montserrat-bold text-blue-600 mb-1">
                    SPACE DIVISION
                  </p>
                  <div className="flex items-center mb-12">
                    <div className="w-16 border-t border-gray-500"></div>
                    <h2 className="text-base md:text-xl ml-4">宇宙事業部</h2>
                  </div>
                  <p className="text-lg md:text-xl text-blue-300 mb-6">
                    宇宙を社会に届ける。学びからエンタメまで、次世代の宇宙体験を形に。
                  </p>
                  <p className="text-gray-300 leading-loose mb-12">
                    私たち宇宙事業部は、学びから衛星データ活用、将来的な人工衛星のエンターテインメント活用まで、
                    宇宙の可能性を社会にひらくための多様な挑戦を行っています。
                  </p>
                </div>

                {/* 解決できる課題（縦線付き） */}
                <div className="mb-24">
                  <div className="border-l-4 border-gray-300 pl-4 mb-6">
                    <p className="text-sm text-gray-300 tracking-widest mb-1">01 — 解決できる課題</p>
                    <h3 className="text-3xl md:text-4xl montserrat-bold tracking-wide">PROBLEM</h3>
                  </div>
                  <p className="text-base mb-6">以下のような課題を解決いたします。</p>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <span className="w-2 h-2 mt-2 mr-4 bg-gray-500 rounded-full"></span>
                      <span className="text-sm md:text-base leading-relaxed">
                        宇宙分野に興味はあるが、何から調べればいいかわからない
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 mt-2 mr-4 bg-gray-500 rounded-full"></span>
                      <span className="text-sm md:text-base leading-relaxed">
                        衛星データを使って何か新しい取り組みをしたいが、技術的に不安がある
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 mt-2 mr-4 bg-gray-500 rounded-full"></span>
                      <span className="text-sm md:text-base leading-relaxed">
                        宇宙×エンタメという未知の領域に挑戦したい
                      </span>
                    </li>
                  </ul>
                </div>

                {/* 提供できるメニュー（縦線付き） */}
                <div className="mb-24">
                  <div className="border-l-4 border-gray-300 pl-4 mb-6">
                    <p className="text-sm text-gray-300 tracking-widest mb-1">02 — 提供できるメニュー</p>
                    <h3 className="text-3xl md:text-4xl montserrat-bold tracking-wide">SERVICE MENU</h3>
                  </div>

                  <ul className="flex flex-wrap gap-2">
                    <li>
                      <span
                        className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                        広報戦略設計
                      </span>
                    </li>
                    <li>
                      <span
                        className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                        映像・動画制作
                      </span>
                    </li>
                    <li>
                      <span
                        className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                        セミナー・イベント企画
                      </span>
                    </li>
                    <li>
                      <span
                        className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                        Webアプリ開発
                      </span>
                    </li>
                    <li>
                      <span
                        className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                        Webサイト管理
                      </span>
                    </li>
                    <li>
                      <span
                        className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                        衛星データ利活用
                      </span>
                    </li>
                    <li>
                      <span
                        className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                        宇宙コンテンツ企画
                      </span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="w-full bg-purple-900/30">
            <div className="max-w-6xl mx-auto px-6 md:px-12 py-24 space-y-32">
              {/* クリエイティブ事業部 */}
              <div id="creative-division" className="scroll-mt-32">
                <div className="mb-8">
                  <p className="text-4xl md:text-6xl montserrat-bold text-purple-600 mb-1">
                    CREATIVE DIVISION
                  </p>
                  <div className="flex items-center mb-12">
                    <div className="w-16 border-t border-gray-500"></div>
                    <h2 className="text-base md:text-xl ml-4">クリエイティブ事業部</h2>
                  </div>
                  <p className="text-lg md:text-xl text-purple-300 mb-6">
                    「伝える」をデザインする。あらゆる表現課題に応えるプロ集団。
                  </p>
                  <p className="text-gray-300 leading-loose mb-12">
                    映像・デザイン・Web・SNSなど、伝える手段を横断的に支援。
                    企画から運用まで一貫して対応し、クライアントの想いを確実に形にします。
                  </p>
                </div>

                {/* 解決できる課題 */}
                <div className="mb-24">
                  <div className="border-l-4 border-gray-300 pl-4 mb-6">
                    <p className="text-sm text-gray-300 tracking-widest mb-1">01 — 解決できる課題</p>
                    <h3 className="text-3xl md:text-4xl montserrat-bold tracking-wide">PROBLEM</h3>
                  </div>
                  <p className="text-base mb-6">以下のような課題を解決いたします。</p>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <span className="w-2 h-2 mt-2 mr-4 bg-gray-500 rounded-full"></span>
                      <span className="text-sm md:text-base leading-relaxed">
                        伝えたいことはあるが、どう表現すればいいか分からない
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 mt-2 mr-4 bg-gray-500 rounded-full"></span>
                      <span className="text-sm md:text-base leading-relaxed">
                        複数の制作会社とやり取りするのが大変
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 mt-2 mr-4 bg-gray-500 rounded-full"></span>
                      <span className="text-sm md:text-base leading-relaxed">
                        制作後の運用や改善まで考慮したい
                      </span>
                    </li>
                  </ul>
                </div>

                {/* 提供できるメニュー */}
                <div className="mb-24">
                  <div className="border-l-4 border-gray-300 pl-4 mb-6">
                    <p className="text-sm text-gray-300 tracking-widest mb-1">02 — 提供できるメニュー</p>
                    <h3 className="text-3xl md:text-4xl montserrat-bold tracking-wide">SERVICE MENU</h3>
                  </div>

                  <div className="flex flex-wrap gap-3">
                    <span
                      className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                      クリエイティブトータル支援
                    </span>
                    <span
                      className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                      映像制作
                    </span>
                    <span
                      className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                      アニメーション制作
                    </span>
                    <span
                      className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                      Web/アプリ制作
                    </span>
                    <span
                      className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                      UI設計・LP制作
                    </span>
                    <span
                      className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                      SNS運用支援
                    </span>
                    <span
                      className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                      プロモーション企画
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <CaseStudiesSection />

          <ServiceFlowSection />

          <FAQSection />

        </section>
      </div>

      <CTASection />
      <Footer />
    </main>
  );
}