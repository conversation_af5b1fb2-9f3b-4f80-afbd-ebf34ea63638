import { Metadata } from 'next';
import Navigation from '../../components/Navigation';
import Footer from '../../components/Footer';
import ContactForm from './ContactForm';

export const metadata: Metadata = {
  title: 'お問い合わせ | 株式会社スペースカウボーイ',
  description: '宇宙事業・映像制作・Web開発に関するご相談・お問い合わせはこちらから。無料ヒヤリングも承っております。',
  openGraph: {
    title: 'お問い合わせ | 株式会社スペースカウボーイ',
    description: '宇宙事業・映像制作・Web開発に関するご相談・お問い合わせはこちらから。無料ヒヤリングも承っております。',
    url: 'https://www.space-cowboy.jp/contact?v=2',
    images: [
      {
        url: 'https://www.space-cowboy.jp/static/img/ogp1200x630.png',
        width: 1200,
        height: 630,
        type: 'image/png',
      },
    ],
    siteName: '株式会社スペースカウボーイ',
    locale: 'ja_JP',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    site: '@cutkey5',
  },
};

export default function ContactPage() {

  return (
    <main className="bg-gray-900/0 text-white contact-page">
      <Navigation />
      
      {/* パララックス背景 */}
      <div className="parallax-bg" id="parallax-bg"></div>
      
      {/* 背景を暗くするオーバーレイ */}
      <div className="fixed inset-0 bg-black/30 z-0"></div>

      {/* メインコンテンツ */}
      <div className="parallax-wrapper">
        <section className="min-h-screen pt-32 pb-0 md:pt-48 md:pb-0 relative z-10">
          <div className="max-w-6xl mx-auto px-6 md:px-12">
            {/* ページタイトル */}
            <div className="mb-24 animate-fadeInUpSlow">
              <h1 className="text-5xl md:text-7xl montserrat-bold text-white mb-8">CONTACT</h1>
              <p className="text-xl md:text-2xl text-blue-300 font-light mb-8">
                お気軽にお問い合わせください
              </p>
              <p className="text-base md:text-lg text-gray-300 leading-loose max-w-4xl">
                あなたのアイデアや質問をお聞かせください。私たちと一緒に宇宙と表現の可能性を広げていきましょう。
              </p>
            </div>
          </div>

          {/* お問い合わせフォーム */}
          <div className="w-full bg-gray-900/80">
            <div className="max-w-4xl mx-auto px-6 md:px-12 py-24">
              <ContactForm />
            </div>
          </div>
        </section>
      </div>

      <Footer />
    </main>
  );
}