'use client';

import Link from 'next/link';
import { useState, FormEvent, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';
import { isRecaptchaEnabled } from '../../utils/recaptchaUtils';

interface FormData {
  name: string;
  company: string;
  email: string;
  phone: string;
  message: string;
  website: string; // honeypot field
  privacy_policy: boolean;
}

function ContactFormContent() {
  const { executeRecaptcha } = useGoogleReCaptcha();
  const searchParams = useSearchParams();
  const [formData, setFormData] = useState<FormData>({
    name: '',
    company: '',
    email: '',
    phone: '',
    message: '',
    website: '', // honeypot field
    privacy_policy: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');

  // URLパラメータからメッセージを取得して初期値に設定
  useEffect(() => {
    const messageParam = searchParams.get('message');
    if (messageParam) {
      setFormData(prev => ({
        ...prev,
        message: decodeURIComponent(messageParam)
      }));
    }
  }, [searchParams]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Check honeypot field (spam protection)
    if (formData.website) {
      return;
    }

    setIsSubmitting(true);
    setSubmitMessage('');

    try {
      // reCAPTCHA処理（環境変数で制御）
      let recaptchaToken = '';
      if (isRecaptchaEnabled() && executeRecaptcha) {
        try {
          recaptchaToken = await executeRecaptcha('contact_form');
        } catch (recaptchaError) {
          console.warn('reCAPTCHA execution failed:', recaptchaError);
          // reCAPTCHAが失敗しても送信を続行
        }
      }
      
      // Google Apps Script にフォームデータを送信
      const gasEndpoint = process.env.NEXT_PUBLIC_CONTACT_GAS_ENDPOINT;
      
      if (!gasEndpoint) {
        throw new Error('GAS endpoint not configured');
      }

      // フォームデータを送信用に整形
      const submissionData = {
        name: formData.name,
        company: formData.company,
        email: formData.email,
        phone: formData.phone,
        message: formData.message,
        timestamp: new Date().toISOString(),
        source: 'Next.js Contact Form',
        ...(recaptchaToken && { recaptchaToken })
      };

      await fetch(gasEndpoint, {
        method: 'POST',
        mode: 'no-cors', // GAS では no-cors を使用
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData)
      });

      // no-cors モードでは response.ok を確認できないので、
      // エラーが投げられなければ成功とみなす
      setSubmitMessage('お問い合わせありがとうございます。後日ご連絡いたします。');
      
      // フォームをリセット
      setFormData({
        name: '',
        company: '',
        email: '',
        phone: '',
        message: '',
        website: '',
        privacy_policy: false,
      });
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitMessage('送信に失敗しました。もう一度お試しください。');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="max-w-2xl mx-auto space-y-8">
      <div className="grid md:grid-cols-2 gap-8">
        {/* お名前 */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium mb-3 text-gray-300">
            お名前 *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            required
            className="w-full px-0 py-4 bg-transparent text-white border-0 border-b-2 border-gray-600 focus:border-blue-400 outline-none transition-colors placeholder-gray-500"
          />
        </div>

        {/* 会社名・団体名 */}
        <div>
          <label htmlFor="company" className="block text-sm font-medium mb-3 text-gray-300">
            会社名・団体名
          </label>
          <input
            type="text"
            id="company"
            name="company"
            value={formData.company}
            onChange={handleInputChange}
            className="w-full px-0 py-4 bg-transparent text-white border-0 border-b-2 border-gray-600 focus:border-blue-400 outline-none transition-colors placeholder-gray-500"
          />
        </div>
      </div>

      {/* メールアドレス */}
      <div>
        <label htmlFor="email" className="block text-sm font-medium mb-3 text-gray-300">
          メールアドレス *
        </label>
        <input
          type="email"
          id="email"
          name="email"
          value={formData.email}
          onChange={handleInputChange}
          required
          className="w-full px-0 py-4 bg-transparent text-white border-0 border-b-2 border-gray-600 focus:border-blue-400 outline-none transition-colors placeholder-gray-500"
        />
      </div>

      {/* 電話番号 */}
      <div>
        <label htmlFor="phone" className="block text-sm font-medium mb-3 text-gray-300">
          電話番号
        </label>
        <input
          type="tel"
          id="phone"
          name="phone"
          value={formData.phone}
          onChange={handleInputChange}
          className="w-full px-0 py-4 bg-transparent text-white border-0 border-b-2 border-gray-600 focus:border-blue-400 outline-none transition-colors placeholder-gray-500"
        />
      </div>

      {/* メッセージ */}
      <div>
        <label htmlFor="message" className="block text-sm font-medium mb-3 text-gray-300">
          お問合せ内容 *
        </label>
        <textarea
          id="message"
          name="message"
          value={formData.message}
          onChange={handleInputChange}
          rows={6}
          required
          className="w-full px-0 py-4 bg-transparent text-white border-0 border-b-2 border-gray-600 focus:border-blue-400 outline-none transition-colors resize-vertical placeholder-gray-500"
          placeholder="お問い合わせ内容を詳しくお聞かせください。"
        />
      </div>

      {/* ハニーポット（スパム対策） */}
      <div style={{ position: 'absolute', left: '-5000px' }} aria-hidden="true">
        <input
          type="text"
          id="website"
          name="website"
          value={formData.website}
          onChange={handleInputChange}
          tabIndex={-1}
          autoComplete="off"
        />
      </div>

      {/* プライバシーポリシー同意 */}
      <div className="flex items-start space-x-3">
        <input
          type="checkbox"
          id="privacy_policy"
          name="privacy_policy"
          checked={formData.privacy_policy}
          onChange={handleInputChange}
          required
          className="mt-1 w-4 h-4 text-blue-600 bg-transparent border-2 border-gray-600 focus:border-blue-400 focus:ring-0 outline-none"
        />
        <label htmlFor="privacy_policy" className="text-sm text-gray-300 leading-relaxed">
          <Link
            href="/privacy"
            target="_blank"
            className="text-blue-400 hover:text-blue-300 underline"
          >
            プライバシーポリシー
          </Link>
          に同意します *
        </label>
      </div>

      {/* 送信ボタン */}
      <div className="text-center pt-8">
        <button
          type="submit"
          disabled={isSubmitting}
          className="hero-cta-button inline-flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span className="text-2xl mr-2">📧</span>
          <span>{isSubmitting ? '送信中...' : '送信する'}</span>
        </button>
        {submitMessage && (
          <div className="mt-4 text-sm text-blue-300">{submitMessage}</div>
        )}
      </div>

      {/* reCAPTCHA v3 表記 */}
      {isRecaptchaEnabled() && (
        <div className="recaptcha-terms mt-8">
          Protected by reCAPTCHA
        </div>
      )}
    </form>
  );
}

export default function ContactForm() {
  return (
    <Suspense fallback={<div className="text-center text-white">読み込み中...</div>}>
      <ContactFormContent />
    </Suspense>
  );
}