import { Metadata } from 'next';
import Navigation from '../../../../components/Navigation';
import AIAnalysis from '../components/AIAnalysis';
import Link from 'next/link';
import Script from 'next/script';

export const metadata: Metadata = {
  title: '送信完了 - イーサポートグループ | 株式会社スペースカウボーイ',
  description: 'ご回答ありがとうございました',
  openGraph: {
    title: '送信完了 - イーサポートグループ | 株式会社スペースカウボーイ',
    description: 'ご回答ありがとうございました',
    url: 'https://www.space-cowboy.jp/games/eSupport/thanks',
    type: 'website',
    images: [
      {
        url: 'https://www.space-cowboy.jp/img/ogp1200x630.png',
        width: 1200,
        height: 630,
        alt: 'スペースカウボーイ',
      },
    ],
  },
};

export default function ESupportThanksPage() {
  return (
    <>
      {/* Google Analytics */}
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
        `}
      </Script>

      <main className="bg-gray-900/70 text-white">
        <Navigation />
        
        {/* パララックス背景 */}
        <div className="parallax-bg" style={{
          backgroundImage: "url('/img/universe.webp')"
        }}></div>
        
        {/* コンテンツ */}
        <div className="relative z-10 min-h-screen flex items-center justify-center px-4">
          <div className="max-w-2xl mx-auto text-center">
            {/* サクセスアニメーション */}
            <div className="mb-8 animate-fade-in">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-green-500 bg-opacity-20 rounded-full mb-6">
                <svg 
                  className="w-8 h-8 text-white" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24" 
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth="2" 
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              
              <h1 className="text-4xl md:text-6xl montserrat-bold text-white mb-6">
                送信完了
              </h1>
              
              <div className="space-y-4 mb-12">
                <p className="text-xl md:text-2xl text-blue-300 font-medium">
                  ご回答ありがとうございました！
                </p>
                <p className="text-base md:text-lg text-gray-300">
                  あなたの貴重なご意見を承りました。
                </p>
                <p className="text-sm md:text-base text-gray-400">
                  イーサポートグループは、皆様とともに、<br />
                  より良いサービスの提供に努めてまいります。
                </p>
              </div>
              
              {/* AI分析コンポーネント */}
              <AIAnalysis />
              
              {/* アクションボタン */}
              <div className="space-y-4 mt-8">
                <Link href="/games/eSupport" className="hero-cta-button inline-block">
                  <span className="flex items-center justify-center">
                    <span className="text-xl mr-2">📝</span>アンケートに戻る
                  </span>
                </Link>
              </div>
            </div>
          </div>
        </div>
        
      </main>

      {/* アニメーション用のスタイル */}
      <style>{`
        @keyframes fade-in {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .animate-fade-in {
          animation: fade-in 0.8s ease-out;
        }
      `}</style>
    </>
  );
}