'use client';

import { useState } from 'react';
import QuestionModal from './QuestionModal';
import type { CardData, FormData } from '../types';


// カードデータの型定義
const cardData: Record<string, CardData> = {
  'optimized_timing': {
    title: 'Optimized Timing',
    question: 'あなたが考える最適なタイミングとは何ですか？',
    icon: '⏰',
    displayTitle: '最適な<br />タイミング'
  },
  'optimized_service': {
    title: 'Optimized Service',
    question: 'あなたが考える最適なサービスとは何ですか？',
    icon: '🎯',
    displayTitle: '最適な<br />サービス'
  },
  'optimized_communication': {
    title: 'Optimized Communication',
    question: 'あなたが考える最適なコミュニケーションとは何ですか？',
    icon: '💬',
    displayTitle: '最適な<br />コミュニケーション'
  },
  'optimized_value': {
    title: 'Optimized Value for Our Area',
    question: 'あなたが考える地域への最適な価値とは何ですか？',
    icon: '🌏',
    displayTitle: '最適な価値を地域に'
  },
  'no1_distributor': {
    title: 'The No.1 Distributor',
    question: 'あなたが考えるNo.1ディストリビューターとは何ですか？',
    icon: '🏆',
    displayTitle: 'No.1ディストリビューター'
  }
};

export default function ESupportGame() {
  const [revealedCard, setRevealedCard] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedTopic, setSelectedTopic] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const handleCardClick = (topic: string) => {
    if (revealedCard === topic) return;

    // Close other revealed cards
    setRevealedCard(topic);

    // Open modal after animation
    setTimeout(() => {
      setSelectedTopic(topic);
      setShowModal(true);
    }, 1800);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedTopic('');
    setRevealedCard(null);
  };

  const handleFormSubmit = async (formData: FormData) => {
    setIsLoading(true);

    try {
      const submitData = new FormData();
      submitData.append(selectedTopic, formData.answer);
      submitData.append('department', formData.department);
      submitData.append('age', formData.age);
      submitData.append('gender', formData.gender);

      await fetch(process.env.NEXT_PUBLIC_ESUPPORT_GAS_ENDPOINT!, {
        method: 'POST',
        body: submitData
      });

      // Redirect to thanks page immediately
      window.location.href = '/games/eSupport/thanks';
    } catch (error) {
      console.error('Error:', error);
      setIsLoading(false);
      alert('送信中にエラーが発生しました。もう一度お試しください。');
    }
  };

  return (
    <>
      {/* eSupport Background Styles */}
      <style jsx global>{`
        body {
          margin: 0;
          padding: 0;
          font-family: system-ui, -apple-system, "Segoe UI", "Noto Sans JP", sans-serif;
          background: var(--color-cosmic-black);
          color: var(--color-cosmic-white);
          line-height: 1.6;
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;
          overflow-x: hidden;
          max-width: 100vw;
        }
        
        html {
          overflow-x: hidden;
          max-width: 100vw;
        }
        :root {
          --color-cosmic-blue: #ff6b35;
          --color-cosmic-teal: #ffa726;
          --color-cosmic-black: #0a0a0a;
          --color-cosmic-white: #ffffff;
          --color-cosmic-dark: #2e1a1a;
          --color-cosmic-glow: rgba(255, 107, 53, 0.3);
          --card-mobile-w: 120px;
          --card-mobile-h: 180px;
          --card-desktop-w: 160px;
          --card-desktop-h: 220px;
        }

        .esupport-container {
          min-height: 100vh;
          position: relative;
          overflow-x: hidden;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 100%;
          max-width: 100vw;
          box-sizing: border-box;
        }
        
        .main {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 100%;
          max-width: 1200px;
          position: relative;
          z-index: 1;
          overflow-x: hidden;
          box-sizing: border-box;
          padding: 0 10px;
        }

        .esupport-bg::before {
          content: '';
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-image: url('/img/universe.webp');
          background-size: cover;
          background-position: center;
          background-attachment: fixed;
          z-index: -2;
          filter: brightness(0.4) contrast(1.2);
        }

        .esupport-bg::after {
          content: '';
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: radial-gradient(ellipse at center, transparent 0%, rgba(10, 10, 10, 0.6) 70%, rgba(10, 10, 10, 0.9) 100%);
          z-index: -1;
          pointer-events: none;
        }

        .logo-background {
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 300px;
          height: 300px;
          background-image: url('/img/eSupportLogo.jpeg');
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          opacity: 0.05;
          z-index: -1;
          pointer-events: none;
        }

        @media (min-width: 768px) {
          .logo-background {
            width: 400px;
            height: 400px;
          }
        }

        .header {
          text-align: center;
          margin: 2rem 0;
          padding: 2rem;
          background: rgba(46, 26, 26, 0.8);
          border-radius: 20px;
          border: 1px solid var(--color-cosmic-teal);
          box-shadow: 0 8px 32px var(--color-cosmic-glow);
          backdrop-filter: blur(10px);
          min-height: 120px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
        }

        .title {
          font-size: clamp(1.8rem, 4vw, 2.5rem);
          background: linear-gradient(135deg, var(--color-cosmic-blue) 0%, var(--color-cosmic-teal) 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          margin: 0 0 0.5rem 0;
          font-weight: 600;
          text-shadow: 0 0 20px var(--color-cosmic-glow);
        }

        .subtitle {
          font-size: clamp(1rem, 2.5vw, 1.2rem);
          color: var(--color-cosmic-white);
          margin: 0;
          opacity: 0.9;
        }

        .howto {
          font-size: 0.9rem;
          color: var(--color-cosmic-white);
          margin: 1rem 0 0 0;
          padding: 0.5rem 1rem;
          background: rgba(255, 107, 53, 0.2);
          border: 1px solid var(--color-cosmic-blue);
          border-radius: 20px;
          display: inline-block;
          backdrop-filter: blur(5px);
        }

        .board {
          display: grid;
          grid-template-columns: repeat(6, 1fr);
          gap: 20px;
          padding: 20px;
          margin-bottom: 1rem;
          justify-items: center;
          background: rgba(46, 26, 26, 0);
          border-radius: 20px;
          backdrop-filter: blur(10px);
        }

        @media (max-width: 767px) {
          .board .card:nth-child(1) { grid-column: 2 / span 2; }
          .board .card:nth-child(2) { grid-column: 4 / span 2; }
          .board .card:nth-child(3) { grid-column: 1 / span 2; }
          .board .card:nth-child(4) { grid-column: 3 / span 2; }
          .board .card:nth-child(5) { grid-column: 5 / span 2; }
        }

        @media (min-width: 768px) {
          .board {
            grid-template-columns: repeat(5, 1fr);
            gap: 24px;
            padding: 24px;
          }
        }

        .card {
          perspective: 1000px;
          width: var(--card-mobile-w);
          height: var(--card-mobile-h);
          cursor: pointer;
          border: none;
          background: none;
          padding: 0;
          position: relative;
          display: inline-block;
          vertical-align: top;
          transition: transform 0.3s ease, filter 0.2s ease;
        }

        @media (min-width: 768px) {
          .card {
            width: var(--card-desktop-w);
            height: var(--card-desktop-h);
          }
        }

        .card:hover:not(.card--revealed) {
          transform: translateY(-10px);
          filter: drop-shadow(0 10px 20px var(--color-cosmic-glow));
        }

        .card__inner {
          width: 100%;
          height: 100%;
          position: relative;
          transform-style: preserve-3d;
          transition: transform 0.5s cubic-bezier(0.22, 1, 0.36, 1);
        }

        .card--revealed .card__inner {
          transform: rotateY(180deg);
        }

        .card__face {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          backface-visibility: hidden;
          border-radius: 16px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          transition: box-shadow 0.2s ease;
          overflow: hidden;
          padding: 1rem;
        }

        .card__face--front {
          background: linear-gradient(135deg, 
            rgba(255, 107, 53, 0.1) 0%, 
            rgba(255, 167, 38, 0.1) 50%, 
            rgba(46, 26, 26, 0.2) 100%);
          border: 2px solid var(--color-cosmic-teal);
          box-shadow: 
            0 8px 32px rgba(255, 167, 38, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
          position: relative;
        }

        .card__face--front::before {
          content: '';
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          background: linear-gradient(45deg, var(--color-cosmic-blue), var(--color-cosmic-teal), var(--color-cosmic-blue));
          border-radius: 16px;
          z-index: -1;
          animation: borderGlow 2s ease-in-out infinite alternate;
        }

        @keyframes borderGlow {
          from {
            opacity: 0.1;
            filter: blur(0px);
          }
          to {
            opacity: 0.5;
            filter: blur(1px);
          }
        }

        .card__face--back {
          background: linear-gradient(135deg, 
            var(--color-cosmic-blue) 0%, 
            var(--color-cosmic-teal) 100%);
          color: white;
          transform: rotateY(180deg);
          text-align: center;
          box-shadow: 
            0 8px 32px rgba(255, 107, 53, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .card__logo {
          width: 60px;
          height: 60px;
          background-image: url('/img/eSupportLogo.jpeg');
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          margin: 0 auto;
          filter: drop-shadow(0 0 10px var(--color-cosmic-glow));
          border-radius: 50%;
        }

        @media (min-width: 768px) {
          .card__logo {
            width: 70px;
            height: 70px;
          }
        }

        .card__text {
          font-size: 0.8rem;
          color: var(--color-cosmic-white);
          margin-top: 0.5rem;
          text-align: center;
          opacity: 0.8;
        }

        .result-title {
          font-size: clamp(1rem, 3vw, 1.3rem);
          font-weight: 600;
          margin-bottom: 0.5rem;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .result-icon {
          width: 32px;
          height: 32px;
          font-size: 32px;
          margin-bottom: 0.5rem;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        .footer {
          margin-top: 2rem;
          text-align: center;
          color: var(--color-cosmic-white);
          font-size: 0.8rem;
          opacity: 0.7;
          padding: 1rem;
          background: rgba(46, 26, 26, 0.5);
          border-radius: 10px;
          backdrop-filter: blur(10px);
        }

        .footer a {
          color: var(--color-cosmic-blue);
          text-decoration: none;
        }

        .footer a:hover {
          text-decoration: underline;
        }

        @media only screen and (max-width: 1024px) {
          .esupport-bg::before {
            background-attachment: scroll;
          }
        }
      `}</style>

      <div className="esupport-container esupport-bg">
        {/* eSupport Logo Background */}
        <div className="logo-background"></div>
        
        {/* Header */}
        <header className="header">
          <h1 className="title">みんなの「最適」で<br />未来を作ろう</h1>
          <p className="subtitle">あなたの最適を教えてください！</p>
          <p className="howto">カードを1枚選んでクリック！</p>
        </header>

        {/* Main Game Area */}
        <main className="main">
          {/* Card Board */}
          <div className="board">
            {Object.entries(cardData).map(([topic, data]) => (
              <div
                key={topic}
                className={`card ${revealedCard === topic ? 'card--revealed' : ''}`}
                onClick={() => handleCardClick(topic)}
              >
                <div className="card__inner">
                  <div className="card__face card__face--front">
                    <div className="card__logo"></div>
                    <div className="card__text">タップで選択</div>
                  </div>
                  <div className="card__face card__face--back">
                    <div className="result-icon">{data.icon}</div>
                    <div 
                      className="result-title"
                      dangerouslySetInnerHTML={{ __html: data.displayTitle }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </main>

        {/* Footer */}
        <footer className="footer">
          <p>&copy; 2025 Space Cowboy Inc. | <a href="/games">一覧に戻る</a></p>
        </footer>
      </div>

      {/* Question Modal */}
      {showModal && (
        <QuestionModal
          isOpen={showModal}
          onClose={handleCloseModal}
          onSubmit={handleFormSubmit}
          question={cardData[selectedTopic]?.question || ''}
          isLoading={isLoading}
        />
      )}
    </>
  );
}