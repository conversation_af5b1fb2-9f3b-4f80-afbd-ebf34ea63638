'use client';

import { useState, useEffect } from 'react';
import { QuestionModalProps, FormData } from '../types';

const departments = [
  'モバイル事業',
  'ソリューション事業', 
  'コールセンター事業',
  'プロモーション事業'
];

const ages = ['10代', '20代', '30代', '40代', '50代', '60代'];
const genders = ['男性', '女性', 'その他'];

export default function QuestionModal({ 
  isOpen, 
  onClose, 
  onSubmit, 
  question, 
  isLoading 
}: QuestionModalProps) {
  const [formData, setFormData] = useState<FormData>({
    answer: '',
    department: '',
    age: '',
    gender: ''
  });
  const [errors, setErrors] = useState<Record<string, boolean>>({});

  useEffect(() => {
    if (isOpen) {
      document.body.classList.add('modal-open');
    } else {
      document.body.classList.remove('modal-open');
    }

    return () => {
      document.body.classList.remove('modal-open');
    };
  }, [isOpen]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: false }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, boolean> = {};
    
    if (!formData.answer.trim()) newErrors.answer = true;
    if (!formData.department) newErrors.department = true;
    if (!formData.age) newErrors.age = true;
    if (!formData.gender) newErrors.gender = true;

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleClose = () => {
    setFormData({ answer: '', department: '', age: '', gender: '' });
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <>
      <style jsx>{`
        .modal-open {
          overflow: hidden;
          position: fixed;
          width: 100%;
        }

        .modal {
          position: fixed;
          z-index: 1000;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.8);
          animation: fadeIn 0.3s ease;
          overflow-y: auto;
          -webkit-overflow-scrolling: touch;
        }

        .modal-content {
          background: linear-gradient(135deg, 
            rgba(46, 26, 26, 0.95) 0%, 
            rgba(255, 107, 53, 0.1) 100%);
          border: 2px solid #ffa726;
          border-radius: 20px;
          margin: 5vh auto;
          padding: 2rem;
          width: 90%;
          max-width: 500px;
          box-shadow: 
            0 20px 60px rgba(0, 0, 0, 0.4),
            0 0 40px rgba(255, 107, 53, 0.3);
          backdrop-filter: blur(20px);
          animation: slideIn 0.3s ease;
          min-height: auto;
          max-height: 90vh;
          overflow-y: auto;
        }

        @media (max-width: 768px) {
          .modal-content {
            margin: 2vh auto;
            padding: 1.5rem;
            width: 95%;
            max-height: 95vh;
            border-radius: 15px;
          }
        }

        .modal-header {
          font-size: 1.5rem;
          color: white;
          margin-bottom: 1.5rem;
          text-align: center;
          background: linear-gradient(135deg, #ff6b35 0%, #ffa726 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .form-group {
          margin-bottom: 1.5rem;
        }

        .form-group label {
          display: block;
          color: white;
          font-weight: 600;
          margin-bottom: 0.5rem;
        }

        .form-group textarea,
        .form-group input {
          width: 100%;
          padding: 0.75rem;
          border: 2px solid rgba(255, 107, 53, 0.3);
          border-radius: 8px;
          font-size: 1rem;
          background: rgba(46, 26, 26, 0.5);
          color: white;
          backdrop-filter: blur(10px);
          transition: border-color 0.3s;
        }

        .form-group textarea:focus,
        .form-group input:focus {
          outline: none;
          border-color: #ff6b35;
          box-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
        }

        .form-group textarea {
          resize: vertical;
          min-height: 100px;
        }

        .button-group {
          display: flex;
          gap: 0.5rem;
          margin-top: 0.5rem;
          overflow-x: auto;
          padding-bottom: 0.5rem;
          -webkit-overflow-scrolling: touch;
        }

        .option-btn {
          padding: 0.5rem 1rem;
          border: 2px solid rgba(255, 107, 53, 0.3);
          border-radius: 25px;
          background: rgba(46, 26, 26, 0.5);
          color: white;
          font-size: 0.9rem;
          cursor: pointer;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
          font-family: inherit;
          white-space: nowrap;
          flex-shrink: 0;
          min-width: fit-content;
        }

        .option-btn:hover {
          border-color: #ff6b35;
          background: rgba(255, 107, 53, 0.1);
          transform: translateY(-1px);
        }

        .option-btn.selected {
          border-color: #ff6b35;
          background: linear-gradient(135deg, #ff6b35 0%, #ffa726 100%);
          color: white;
          box-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
        }

        .error-message {
          color: #ff6b6b;
          font-size: 0.75rem;
          margin-top: 0.25rem;
          display: block;
        }

        .modal-footer {
          display: flex;
          gap: 1rem;
          margin-top: 2rem;
        }

        .btn {
          padding: 0.75rem 1.5rem;
          border: none;
          border-radius: 25px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          font-family: inherit;
          flex: 1;
          position: relative;
          overflow: hidden;
        }

        .btn--primary {
          background: linear-gradient(135deg, #ff6b35 0%, #ffa726 100%);
          color: white;
          border: 1px solid #ffa726;
          box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .btn--primary:hover:not(:disabled) {
          background: linear-gradient(135deg, #ffa726 0%, #ff6b35 100%);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
        }

        .btn--primary:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }

        .btn--secondary {
          background: transparent;
          color: #ff6b35;
          border: 2px solid #ff6b35;
          backdrop-filter: blur(10px);
        }

        .btn--secondary:hover {
          background: #ff6b35;
          color: white;
          box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideIn {
          from {
            transform: translateY(-20px);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }

        .loading-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.9);
          z-index: 2000;
          display: flex;
          justify-content: center;
          align-items: center;
          animation: fadeIn 0.3s ease;
        }

        .loading-content {
          text-align: center;
          color: white;
          padding: 2rem;
          background: rgba(46, 26, 26, 0.8);
          border-radius: 20px;
          border: 2px solid #ffa726;
          box-shadow: 0 0 40px rgba(255, 107, 53, 0.3);
          backdrop-filter: blur(20px);
        }

        .loading-spinner {
          width: 50px;
          height: 50px;
          border: 4px solid rgba(255, 167, 38, 0.3);
          border-top: 4px solid #ff6b35;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 1rem auto;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .loading-text {
          font-size: 1.2rem;
          color: white;
          margin-bottom: 0.5rem;
        }

        .loading-subtext {
          font-size: 0.9rem;
          color: #ff6b35;
          opacity: 0.8;
        }
      `}</style>

      <div className="modal" onClick={(e) => e.target === e.currentTarget && handleClose()}>
        <div className="modal-content">
          <h2 className="modal-header">{question}</h2>
          
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="answer">あなたの考えを教えてください</label>
              <textarea
                id="answer"
                value={formData.answer}
                onChange={(e) => handleInputChange('answer', e.target.value)}
                placeholder="こちらに入力してください"
                required
              />
              {errors.answer && (
                <span className="error-message">※回答を入力してください</span>
              )}
            </div>
            
            <div className="form-group">
              <label>部署</label>
              <div className="button-group">
                {departments.map((dept) => (
                  <button
                    key={dept}
                    type="button"
                    className={`option-btn ${formData.department === dept ? 'selected' : ''}`}
                    onClick={() => handleInputChange('department', dept)}
                  >
                    {dept}
                  </button>
                ))}
              </div>
              {errors.department && (
                <span className="error-message">※部署を選択してください</span>
              )}
            </div>
            
            <div className="form-group">
              <label>年齢</label>
              <div className="button-group">
                {ages.map((age) => (
                  <button
                    key={age}
                    type="button"
                    className={`option-btn ${formData.age === age ? 'selected' : ''}`}
                    onClick={() => handleInputChange('age', age)}
                  >
                    {age}
                  </button>
                ))}
              </div>
              {errors.age && (
                <span className="error-message">※年齢を選択してください</span>
              )}
            </div>
            
            <div className="form-group">
              <label>性別</label>
              <div className="button-group">
                {genders.map((gender) => (
                  <button
                    key={gender}
                    type="button"
                    className={`option-btn ${formData.gender === gender ? 'selected' : ''}`}
                    onClick={() => handleInputChange('gender', gender)}
                  >
                    {gender}
                  </button>
                ))}
              </div>
              {errors.gender && (
                <span className="error-message">※性別を選択してください</span>
              )}
            </div>
            
            <div className="modal-footer">
              <button type="button" className="btn btn--secondary" onClick={handleClose}>
                キャンセル
              </button>
              <button type="submit" className="btn btn--primary" disabled={isLoading}>
                送信する
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="loading-overlay">
          <div className="loading-content">
            <div className="loading-spinner"></div>
            <div className="loading-text">送信中...</div>
            <div className="loading-subtext">少々お待ちください</div>
          </div>
        </div>
      )}
    </>
  );
}