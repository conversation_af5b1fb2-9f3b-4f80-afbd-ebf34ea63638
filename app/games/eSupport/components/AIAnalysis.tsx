'use client';

import { useState } from 'react';
import type { AIAnalysisProps, AIAnalysisResult } from '../types';

export default function AIAnalysis({ onAnalysisComplete }: AIAnalysisProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<AIAnalysisResult | null>(null);
  const [isAnalyzed, setIsAnalyzed] = useState(false);

  const handleAnalysis = async () => {
    setIsLoading(true);

    try {
      const response = await fetch(process.env.NEXT_PUBLIC_CLOUD_RUN_API!);
      const data: AIAnalysisResult = await response.json();

      setResult(data);
      setIsAnalyzed(true);

      if (onAnalysisComplete) {
        onAnalysisComplete(data);
      }
    } catch (error) {
      console.error('API Error:', error);
      alert('APIサーバーに接続できません。サーバーが起動しているか確認してください。');
      setResult({
        success: false,
        error: 'APIサーバーに接続できません'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <style jsx>{`
        .ai-analysis-container {
          margin: 2rem 0;
        }

        .ai-summary-btn {
          display: inline-block;
          padding: 0.75rem 2rem;
          background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
          color: white;
          border: none;
          border-radius: 50px;
          font-size: 1.1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          text-decoration: none;
          box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
          position: relative;
          overflow: hidden;
          width: 100%;
          max-width: 300px;
          text-align: center;
          font-family: inherit;
        }

        .ai-summary-btn:hover:not(:disabled) {
          background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
        }

        .ai-summary-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }

        .ai-summary-btn::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
          transition: left 0.5s;
        }

        .ai-summary-btn:hover::before {
          left: 100%;
        }

        .ai-summary-result {
          margin-top: 2rem;
          padding: 1.5rem;
          background: rgba(31, 41, 55, 0.7);
          border-radius: 1rem;
          border: 1px solid rgba(107, 114, 128, 1);
          backdrop-filter: blur(10px);
        }

        .ai-summary-result h3 {
          font-size: 1.25rem;
          font-weight: bold;
          color: white;
          margin-bottom: 1rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .summary-content {
          color: rgb(209, 213, 219);
          line-height: 1.75;
          margin-bottom: 1rem;
        }

        .summary-stats {
          margin-top: 1rem;
          font-size: 0.875rem;
          color: rgb(156, 163, 175);
        }

        .summary-stats-grid {
          display: flex;
          flex-wrap: wrap;
          gap: 1rem;
        }

        .loading-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.8);
          z-index: 50;
          display: flex;
          align-items: center;
          justify-content: center;
          animation: fadeIn 0.3s ease;
        }

        .loading-content {
          background: rgba(31, 41, 55, 0.95);
          border-radius: 1rem;
          padding: 2rem;
          text-align: center;
          border: 1px solid rgb(75, 85, 99);
          backdrop-filter: blur(10px);
        }

        .loading-spinner {
          display: inline-block;
          width: 3rem;
          height: 3rem;
          border: 2px solid rgb(59, 130, 246);
          border-top: 2px solid transparent;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 1rem;
        }

        .loading-text {
          color: white;
          font-size: 1.125rem;
          font-weight: 600;
        }

        .loading-subtext {
          color: rgb(209, 213, 219);
          font-size: 0.875rem;
          margin-top: 0.5rem;
        }

        .error-message {
          color: #ef4444;
          background: rgba(239, 68, 68, 0.1);
          border: 1px solid rgba(239, 68, 68, 0.3);
          border-radius: 0.5rem;
          padding: 1rem;
          margin-top: 1rem;
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>

      <div className="ai-analysis-container">
        {!isAnalyzed && (
          <button
            onClick={handleAnalysis}
            disabled={isLoading}
            className="ai-summary-btn"
          >
            <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <span style={{ fontSize: '1.25rem', marginRight: '0.5rem' }}>🤖</span>
              AI要約を見る
            </span>
          </button>
        )}

        {isAnalyzed && (
          <button
            onClick={handleAnalysis}
            disabled={isLoading}
            className="ai-summary-btn"
            style={{
              background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
              boxShadow: '0 4px 15px rgba(16, 185, 129, 0.3)'
            }}
          >
            <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <span style={{ fontSize: '1.25rem', marginRight: '0.5rem' }}>✅</span>
              分析完了
            </span>
          </button>
        )}

        {result && result.success && (
          <div className="ai-summary-result">
            <h3>
              <span style={{ fontSize: '1.5rem', marginRight: '0.5rem' }}>🧠</span>
              従業員意識のAI分析結果
            </h3>
            <div className="summary-content">
              <p>{result.data?.summary}</p>
            </div>
            <div className="summary-stats">
              <div className="summary-stats-grid">
                <span>📊 回答者数: {result.data?.total_responses}人</span>
                <span>🏢 部署数: {result.data?.departments ? Object.keys(result.data.departments).length : 0}</span>
                <span>⏰ 分析日時: {result.data?.timestamp ? new Date(result.data.timestamp).toLocaleString('ja-JP') : ''}</span>
              </div>
            </div>
          </div>
        )}

        {result && !result.success && (
          <div className="error-message">
            <strong>AI分析でエラーが発生しました:</strong> {result.error}
          </div>
        )}
      </div>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="loading-overlay">
          <div className="loading-content">
            <div className="loading-spinner"></div>
            <div className="loading-text">AI分析中...</div>
            <div className="loading-subtext">アンケート結果を分析しています</div>
          </div>
        </div>
      )}
    </>
  );
}