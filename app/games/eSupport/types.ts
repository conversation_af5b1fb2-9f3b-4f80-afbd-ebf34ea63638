export interface CardData {
  title: string;
  question: string;
  icon: string;
  displayTitle: string;
}

export interface FormData {
  answer: string;
  department: string;
  age: string;
  gender: string;
}

export interface QuestionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (formData: FormData) => void;
  question: string;
  isLoading: boolean;
}

export interface AIAnalysisProps {
  onAnalysisComplete?: (data: AIAnalysisResult) => void;
}

export interface AIAnalysisResult {
  success: boolean;
  data?: {
    summary: string;
    total_responses: number;
    departments: Record<string, number>;
    timestamp: string;
  };
  error?: string;
}