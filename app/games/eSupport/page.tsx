import { Metadata } from 'next';
import ESupportGame from './components/ESupportGame';
import Script from 'next/script';

export const metadata: Metadata = {
  title: 'イーサポートグループ - あなたの「最適」を教えてください | 株式会社スペースカウボーイ',
  description: '5つのテーマから1つを選んで、あなたの最適を教えてください',
  openGraph: {
    title: 'イーサポートグループ - あなたの「最適」を教えてください | 株式会社スペースカウボーイ',
    description: 'イーサポートグループと一緒に最高の価値を創造していきましょう',
    url: 'https://www.space-cowboy.jp/games/eSupport',
    type: 'website',
    images: [
      {
        url: 'https://www.space-cowboy.jp/img/ogp1200x630.png',
        width: 1200,
        height: 630,
        alt: 'スペースカウボーイ',
      },
    ],
  },
};

export default function ESupportPage() {
  return (
    <>
      {/* Google Analytics */}
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
        `}
      </Script>

      {/* eSupportゲーム（ナビゲーションやフッター無し、独立したレイアウト） */}
      <ESupportGame />
    </>
  );
}