import { Metadata } from 'next';
import CreativeToolLayout from '../../../components/CreativeToolLayout';
import SquareCropper from '../../../components/SquareCropper';

export const metadata: Metadata = {
  title: '正方形クロップツール - 無料で画像を正方形に切り抜き | 株式会社スペースカウボーイ',
  description: '無料の正方形クロップツールで画像を1:1に切り抜き。Instagram、LINE、Twitter用の正方形画像を簡単作成。中央部分を自動で美しくクロップ。',
  keywords: ['正方形クロップ', '画像切り抜き', 'SNS用画像', 'Instagram', 'Twitter', '1:1', '正方形画像', '無料ツール'],
  openGraph: {
    title: '正方形クロップツール - 無料で画像を正方形に切り抜き',
    description: '無料の正方形クロップツールで画像を1:1に切り抜き。Instagram、LINE、Twitter用の正方形画像を簡単作成。',
    url: 'https://www.space-cowboy.jp/games/square-cropper/',
    type: 'website',
    images: [
      {
        url: 'https://www.space-cowboy.jp/img/utility_03.jpg',
        width: 1200,
        height: 630,
        alt: '正方形クロップツールのプレビュー',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@cutkey5',
    title: '正方形クロップツール - 無料で画像を正方形に切り抜き',
    description: '無料の正方形クロップツールで画像を1:1に切り抜き。Instagram、LINE、Twitter用の正方形画像を簡単作成。',
  },
  alternates: {
    canonical: 'https://www.space-cowboy.jp/games/square-cropper/',
  },
};

const toolUsageSteps = [
  {
    icon: '📁',
    title: '1. 画像選択',
    description: 'クロップしたい画像をドラッグ&ドロップまたはクリックして選択'
  },
  {
    icon: '✂️',
    title: '2. 自動クロップ',
    description: '短い辺に合わせて中央部分を正方形に自動クロップ'
  },
  {
    icon: '💾',
    title: '3. ダウンロード',
    description: '正方形にクロップされた画像をダウンロード'
  }
];

export default function SquareCropperPage() {
  return (
    <CreativeToolLayout
      title="正方形クロップツール"
      subtitle="縦長・横長の画像を正方形にクロップ"
      description="縦長・横長の画像を正方形にクロップします。短い辺に合わせて中央部分を自動で切り取り、SNS投稿などに最適な正方形画像を作成できます。"
      currentToolId="square-cropper"
      toolUsageSteps={toolUsageSteps}
    >
      <SquareCropper />
    </CreativeToolLayout>
  );
}