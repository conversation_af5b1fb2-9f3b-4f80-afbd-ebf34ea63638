'use client';

import GameCard from '../../components/GameCard';
import { gamesData, toolsData } from '../../data/gameData';

export default function GamesContent() {
  return (
    <div className="mb-24 space-y-24">
      {/* ゲームセクション */}
      <div>
        <div className="border-l-4 border-gray-300 pl-4 mb-12">
          <p className="text-sm text-gray-300 tracking-widest mb-1">01 — ゲーム</p>
          <h3 className="text-3xl md:text-4xl text-gray-300 montserrat-bold tracking-wide">GAMES</h3>
        </div>

        <div className="max-w-7xl mx-auto py-0">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8">
            {gamesData.map((game) => (
              <GameCard key={game.id} game={game} />
            ))}
          </div>
        </div>
      </div>

      {/* クリエイティブ支援ツールセクション */}
      <div>
        <div className="border-l-4 border-purple-400 pl-4 mb-12">
          <p className="text-sm text-purple-300 tracking-widest mb-1">02 — クリエイティブ支援ツール</p>
          <h3 className="text-3xl md:text-4xl text-purple-300 montserrat-bold tracking-wide">CREATIVE TOOLS</h3>
        </div>

        <div className="max-w-7xl mx-auto py-0">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8">
            {toolsData.map((tool) => (
              <GameCard key={tool.id} game={tool} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}