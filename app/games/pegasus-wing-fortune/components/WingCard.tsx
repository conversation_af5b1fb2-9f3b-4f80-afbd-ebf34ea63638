'use client';

import { Wing, GameState } from '../types/game';

interface WingCardProps {
  wing: Wing;
  wingIndex: number;
  gameState: GameState;
  onWingClick: (wingIndex: number) => void;
  className?: string;
}

export default function WingCard({ wing, wingIndex, gameState, onWingClick, className }: WingCardProps) {
  // 選択されたカードは即座に、他のカードはREVEAL_ALLフェーズでめくる
  const isRevealed = (gameState.selectedWingIndex === wingIndex && gameState.phase !== 'IDLE') || 
                    (gameState.phase === 'REVEAL_ALL' || gameState.phase === 'COMPLETE');
  const isSelected = gameState.selectedWingIndex === wingIndex;
  const isDisabled = gameState.phase !== 'IDLE';

  const handleClick = () => {
    if (!isDisabled && !isRevealed) {
      onWingClick(wingIndex);
    }
  };

  // 結果に応じたアイコンを取得
  const getResultIcon = () => {
    switch (wing.key) {
      case 'DAIKICHI':
      case 'KICHI':
      case 'CHUKICHI':
      case 'SHOKICHI':
        return (
          <svg 
            className="w-8 h-8 result-icon" 
            viewBox="0 0 24 24" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <filter id="iconGlow">
                <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>
            <path 
              d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" 
              fill="currentColor" 
              filter="url(#iconGlow)"
            />
          </svg>
        );
      case 'TONKOTSU':
        return (
          <svg 
            className="w-8 h-8 result-icon" 
            viewBox="0 0 24 24" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <filter id="ramenGlow">
                <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>
            <ellipse 
              cx="12" cy="18" rx="8" ry="4" 
              fill="none" stroke="currentColor" strokeWidth="2" 
              filter="url(#ramenGlow)"
            />
            <path 
              d="M4 14 Q12 12, 20 14" 
              fill="none" stroke="currentColor" strokeWidth="2"
            />
            <path 
              d="M6 14 L6 8 M10 14 L10 6 M14 14 L14 6 M18 14 L18 8" 
              stroke="currentColor" strokeWidth="1"
            />
            <path 
              d="M8 8 L8 4 M12 6 L12 2 M16 6 L16 2" 
              stroke="currentColor" strokeWidth="1"
            />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <button
      onClick={handleClick}
      disabled={isDisabled}
      aria-label={`羽 ${wingIndex + 1}`}
      aria-pressed={isRevealed}
      className={`
        wing-card cursor-pointer focus:outline-none focus:ring-4 focus:ring-cyan-400/50 rounded-2xl
        ${isSelected ? 'wing-selected' : ''}
        ${isRevealed ? 'wing-revealed' : ''}
        ${isDisabled ? 'wing-disabled cursor-not-allowed opacity-75' : ''}
        ${className || ''}
      `}
    >
      <div className="wing-inner">
        {/* カード前面 - ペガサスの羽 */}
        <div className="card-front">
          <svg 
            className="wing-icon w-12 h-12 md:w-16 md:h-16" 
            viewBox="0 0 300 374.56" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <radialGradient 
                id={`cardPegasusGradient${wingIndex}`} 
                cx="150" cy="187.28" r="169.67" 
                gradientUnits="userSpaceOnUse"
              >
                <stop offset="0" stopColor="#2ea7e0"/>
                <stop offset=".5" stopColor="#00a8b5"/>
                <stop offset="1" stopColor="#2ea7e0"/>
              </radialGradient>
              <filter id={`cardCosmicGlow${wingIndex}`}>
                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>
            <path 
              fill={`url(#cardPegasusGradient${wingIndex})`} 
              filter={`url(#cardCosmicGlow${wingIndex})`}
              d="M49.29,72.62l25,25.49c10.91,2.11,22.57.03,33.69,1.13l40.3,41.28-17.41-.25,12.32,29.02c-4.29,10.34-9.07,20.52-13.41,30.83-.44,1.05-1.39,2.72-1.16,3.82,3.82-4.9,9.43-9.41,12.9-14.46,2.93-4.27,16.19-28.3,16.61-32.18.13-1.23-.44-2.17-.43-3.33.1-25.5-1.38-50.93-1.23-76.38,38.94-24.94,79.23-47.79,118-73.02l8.61-4.57-1.24,43.78-63.43,48.5,64.16-26.61c-1.55,10.59-1.6,21.42-3.04,32.02-.25,1.85-.3,4.77-1.42,6.04l-56.72,33.81,55.21-11.19-14.49,34.01c-1.12,1.42-7.48,3.08-9.63,3.8-13.59,4.57-27.4,8.54-41.04,12.93l43.27,2.73-18.93,22.36c-20.25,4.72-40.51,9.32-60.66,14.46-.56.14-1.61-.03-1.49.73,19.77.57,39.55.83,59.34,2.1,6.75,4.81,11.92,11.64,18.11,17.2l29.14,1.59,19.75,33.09v61.93l-37.05-41.79-2.22-35-9.02-1.59-4.83,19.55-12.89,17.8c4.63,6.91,10.89,13.33,15.48,20.19.73,1.09,1.84,2.21,1.69,3.56l-21.07,45.62-1.22,12.95h-36.31l20.61-23.42,9.14-29-40.69-37.37v-25.12l-10.04,16.57c-1.09,1.69-.21,5.12-2.01,5.95-15.39,2.77-30.89,5.04-46.25,7.97-2.96.56-16.51,2.72-17.82,4.06l-37.06,64.91c-2.1,4.87-2.72,10.26-3.71,15.44H29.89l26.24-31.22,17.04-59.81c-2.17,1.57-4.88,7.05-7.66,6.94-13.5-4.41-26.77-9.58-40.27-13.99-.49-.16-.47-.73-1.08.07l-2.65,22.33,16.35,6.04-7.21,31.83-28.14-28.19,3.1-55.54.71-.76,41,1.93c-1.55-6.97-2.42-14.29-2.24-21.39l18.72-43.95c1.75-2.17,8.72-2.73,11.36-4.56,2.08-1.45,3.77-5.64,6.23-7.2l1.74-14.92-13.95,15.64c-4.88,1.8-13.31,1.64-17.75,3.64-1.04.47-11.36,8.25-12.53,9.36-2.02,1.91-8.48,12.82-9.33,13.09l-23.32-6.39c-3.01-5.12-4.46-11.06-6.24-16.69,6.41-12.81,13.89-25.05,20.52-37.72,4.77-9.11,5.45-12.16,12.6-20.23,5.19-5.86,10.93-11.33,16.17-17.15v-33.08Z"
            />
          </svg>
        </div>

        {/* カード背面 - 結果 */}
        <div className={`card-back result-${wing.key.toLowerCase()}`}>
          <div className="flex flex-col items-center justify-center h-full">
            <div className="mb-2">
              {getResultIcon()}
            </div>
            <div className="text-xl md:text-2xl font-bold mb-2 text-shadow">
              {wing.name}
            </div>
            <div className="text-xs md:text-sm opacity-90 text-center leading-tight px-2">
              {wing.message}
            </div>
          </div>
        </div>
      </div>
    </button>
  );
}