'use client';

import { useCallback } from 'react';
import { useGameLogic } from '../hooks/useGameLogic';
import { useGameStats } from '../hooks/useGameStats';
import { useShareResult } from '../hooks/useShareResult';
import GameHeader from './GameHeader';
import GameBoard from './GameBoard';
import GameControls from './GameControls';
import GameStats from './GameStats';

export default function PegasusGame() {
  const { gameState, CONFIG, revealWing, resetGame, getSelectedResult } = useGameLogic();

  // 統計更新用コールバック
  const updateStats = useCallback(() => {
    // この関数は useGameStats で使用される
    // 実際の統計更新は useGameLogic 内で処理される
  }, []);

  // 統計管理フック
  useGameStats(gameState.stats, updateStats);

  // シェア機能フック
  const { shareResult, getShareButtonText, isSharing } = useShareResult();

  // 結果をシェア
  const handleShare = useCallback(() => {
    const selectedResult = getSelectedResult();
    shareResult(selectedResult);
  }, [getSelectedResult, shareResult]);

  return (
    <div className="flex flex-col items-center min-h-screen px-4 py-8 relative z-20">
      {/* ゲームヘッダー */}
      <GameHeader 
        gameState={gameState}
        CONFIG={CONFIG}
      />

      {/* メインゲームエリア */}
      <div className="flex-1 flex flex-col items-center w-full max-w-6xl">
        {/* ゲームボード */}
        <GameBoard 
          gameState={gameState}
          onWingClick={revealWing}
        />

        {/* ゲームコントロール */}
        <GameControls 
          gameState={gameState}
          onReset={resetGame}
          onShare={handleShare}
          shareButtonText={getShareButtonText()}
          isSharing={isSharing}
        />

        {/* 統計表示 */}
        <GameStats 
          gameState={gameState}
          CONFIG={CONFIG}
        />
      </div>

      {/* フッター */}
      <footer className="mt-8 text-center text-white/70 text-sm p-4 bg-gray-900/50 rounded-lg backdrop-blur-md">
        <p>
          &copy; 2025 Space Cowboy Inc. | 
          <a 
            href="/games" 
            className="ml-2 text-blue-300 hover:text-blue-200 underline transition-colors"
          >
            一覧に戻る
          </a>
        </p>
      </footer>
    </div>
  );
}