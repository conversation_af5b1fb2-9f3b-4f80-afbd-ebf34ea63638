'use client';

import { GameState } from '../types/game';

interface GameControlsProps {
  gameState: GameState;
  onReset: () => void;
  onShare: () => void;
  shareButtonText: string;
  isSharing: boolean;
}

export default function GameControls({ 
  gameState, 
  onReset, 
  onShare, 
  shareButtonText,
  isSharing 
}: GameControlsProps) {
  const canReset = gameState.phase !== 'IDLE';
  const canShare = gameState.phase === 'COMPLETE' && gameState.selectedWingIndex >= 0;

  return (
    <section className="mb-6">
      <div className="flex gap-4 justify-center flex-wrap">
        {/* もう一度プレイボタン */}
        <button
          onClick={onReset}
          disabled={!canReset}
          className={`
            cosmic-btn-primary
            px-6 py-3 rounded-full font-semibold text-white transition-all duration-300
            min-w-[120px] flex items-center justify-center gap-2
            ${canReset 
              ? 'opacity-100 cursor-pointer hover:-translate-y-1 active:scale-95' 
              : 'opacity-50 cursor-not-allowed'
            }
          `}
        >
          <span className="text-xl">🔄</span>
          もう一度
        </button>

        {/* シェアボタン */}
        <button
          onClick={onShare}
          disabled={!canShare || isSharing}
          className={`
            cosmic-btn-secondary
            px-6 py-3 rounded-full font-semibold transition-all duration-300
            min-w-[120px] flex items-center justify-center gap-2
            ${canShare && !isSharing
              ? 'opacity-100 cursor-pointer hover:-translate-y-1 active:scale-95' 
              : 'opacity-50 cursor-not-allowed'
            }
          `}
        >
          <span className="text-xl">
            {isSharing ? '⏳' : shareButtonText === 'コピー済み！' ? '✅' : '📤'}
          </span>
          {shareButtonText}
        </button>
      </div>
    </section>
  );
}