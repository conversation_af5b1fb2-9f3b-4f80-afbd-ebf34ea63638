'use client';

import { GameState } from '../types/game';
import WingCard from './WingCard';

interface GameBoardProps {
  gameState: GameState;
  onWingClick: (wingIndex: number) => void;
}

export default function GameBoard({ gameState, onWingClick }: GameBoardProps) {
  // Hydration完了まで空の状態を表示
  if (gameState.wings.length === 0) {
    return (
      <div className="mb-8">
        <div className="bg-gray-900/20 rounded-2xl p-5 md:p-6 backdrop-blur-sm">
          <div className="text-center py-16 text-gray-400">
            ゲームを読み込み中...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-2">
      {/* ゲームボード - 元のCSSレイアウトを再現 */}
      <div className="bg-gray-900/20 rounded-2xl p-5 md:p-6 backdrop-blur-sm">
        {/* カスタムCSSクラス使用でレスポンシブ対応 */}
        <div className="original-game-board">
          {gameState.wings.map((wing, index) => (
            <WingCard
              key={index}
              wing={wing}
              wingIndex={index}
              gameState={gameState}
              onWingClick={onWingClick}
              className="original-wing-card"
            />
          ))}
        </div>
      </div>
    </div>
  );
}