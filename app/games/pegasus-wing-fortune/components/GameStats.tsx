'use client';

import { GameState, GameConfig } from '../types/game';

interface GameStatsProps {
  gameState: GameState;
  CONFIG: GameConfig;
}

export default function GameStats({ gameState, CONFIG }: GameStatsProps) {
  const getBestResultName = () => {
    if (!gameState.stats.bestResult) return '-';
    return CONFIG.RESULTS[gameState.stats.bestResult].name;
  };

  return (
    <div className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-cyan-500/20">
      <div className="flex justify-center gap-8 text-center flex-wrap">
        {/* プレイ回数 */}
        <div className="flex flex-col items-center">
          <span className="text-2xl md:text-3xl font-bold text-cyan-300">
            {gameState.stats.playCount}
          </span>
          <span className="text-sm text-gray-300 mt-1">
            プレイ回数
          </span>
        </div>

        {/* 区切り線 */}
        <div className="hidden sm:block w-px bg-gray-600 self-stretch"></div>

        {/* 最高結果 */}
        <div className="flex flex-col items-center">
          <span className="text-2xl md:text-3xl font-bold text-yellow-400">
            {getBestResultName()}
          </span>
          <span className="text-sm text-gray-300 mt-1">
            最高結果
          </span>
        </div>
      </div>

    </div>
  );
}