'use client';

import { useState, useEffect } from 'react';
import { GameState, GameConfig } from '../types/game';

interface GameHeaderProps {
  gameState: GameState;
  CONFIG: GameConfig;
}

export default function GameHeader({ gameState }: GameHeaderProps) {
  const [headerTitle, setHeaderTitle] = useState('ペガサスの羽みくじ');
  const [headerSubtitle, setHeaderSubtitle] = useState('羽を1枚選んで運勢をチェック！');
  const [showResult, setShowResult] = useState(false);

  // 選択された結果を表示する
  useEffect(() => {
    if (gameState.phase === 'PICKED' && gameState.selectedWingIndex >= 0) {
      const selectedWing = gameState.wings[gameState.selectedWingIndex];
      if (selectedWing) {
        // 結果をヘッダーに表示
        setShowResult(true);
        setHeaderTitle(selectedWing.name);
        setHeaderSubtitle(selectedWing.message);

        // 10秒後に元に戻す
        const timer = setTimeout(() => {
          setShowResult(false);
          setHeaderTitle('ペガサスの羽みくじ');
          setHeaderSubtitle('羽を1枚選んで運勢をチェック！');
        }, 10000);

        return () => clearTimeout(timer);
      }
    }
  }, [gameState.phase, gameState.selectedWingIndex, gameState.wings]);

  return (
    <header className="text-center mb-8 p-6 bg-gray-900/70 rounded-2xl border border-cyan-500/30 shadow-2xl backdrop-blur-md max-w-2xl">
      {/* 固定高さでレイアウト安定化 */}
      <div className="h-[120px] flex flex-col justify-center items-center">
        <h1 
          className={`text-3xl md:text-5xl font-bold mb-4 cosmic-title transition-all duration-500 ${
            showResult ? 'result-animation' : ''
          }`}
        >
          {headerTitle}
        </h1>
        
        <p 
          className={`text-lg md:text-xl text-white/90 transition-all duration-500 ${
            showResult ? 'result-animation' : ''
          }`}
        >
          {headerSubtitle}
        </p>
      </div>
    </header>
  );
}