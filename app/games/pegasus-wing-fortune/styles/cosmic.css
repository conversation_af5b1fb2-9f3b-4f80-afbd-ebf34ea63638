/* ペガサスの羽みくじ - 宇宙テーマスタイル */

/* CSS Variables for Space Theme */
.cosmic-theme {
  /* Cosmic Color Palette */
  --color-cosmic-blue: #2ea7e0;
  --color-cosmic-teal: #00a8b5;
  --color-cosmic-black: #0a0a0a;
  --color-cosmic-white: #ffffff;
  --color-cosmic-dark: #1a1a2e;
  --color-cosmic-glow: rgba(46, 167, 224, 0.3);
  
  /* Transition Settings */
  --transition-duration: 0.5s;
  --transition-easing: cubic-bezier(0.22, 1, 0.36, 1);
  
  /* Wing Card Dimensions */
  --wing-mobile-w: 120px;
  --wing-mobile-h: 180px;
  --wing-desktop-w: 160px;
  --wing-desktop-h: 220px;
}

/* Cosmic Background */
.cosmic-background {
  position: relative;
  background-image: url('/img/universe.webp');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
}

.cosmic-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, rgba(10, 10, 10, 0.8) 0%, rgba(10, 10, 10, 0.6) 70%, rgba(10, 10, 10, 0.8) 100%);
  pointer-events: none;
  z-index: 1;
}

/* Header Animations */
.result-animation {
  animation: resultPulse 1s ease-in-out;
}

.header-result-animation {
  animation: headerPulse 0.5s cubic-bezier(0.22, 1, 0.36, 1);
}

@keyframes resultPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes headerPulse {
  0% { transform: scale(1); }
  50% { 
    transform: scale(1.05);
    box-shadow: 0 12px 40px var(--color-cosmic-glow);
  }
  100% { transform: scale(1); }
}

/* Fade Animations */
.fade-out {
  animation: fadeOut 0.5s ease-out forwards;
}

.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Original Game Board Layout - レスポンシブ対応 */
.original-game-board {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
  justify-items: center;
}

/* Mobile layout: 2 cards on first row, 3 on second row */
@media (max-width: 767px) {
  .original-game-board .original-wing-card:nth-child(1) {
    grid-column: 2 / span 2;
  }
  .original-game-board .original-wing-card:nth-child(2) {
    grid-column: 4 / span 2;
  }
  .original-game-board .original-wing-card:nth-child(3) {
    grid-column: 1 / span 2;
  }
  .original-game-board .original-wing-card:nth-child(4) {
    grid-column: 3 / span 2;
  }
  .original-game-board .original-wing-card:nth-child(5) {
    grid-column: 5 / span 2;
  }
}

@media (min-width: 768px) {
  .original-game-board {
    grid-template-columns: repeat(5, 1fr);
    gap: 24px;
  }
}

/* Wing Card 3D Effects */
.wing-card {
  perspective: 1000px;
  width: var(--wing-mobile-w);
  height: var(--wing-mobile-h);
  transition: transform 0.3s ease, filter 0.2s ease;
}

@media (min-width: 768px) {
  .wing-card {
    width: var(--wing-desktop-w);
    height: var(--wing-desktop-h);
  }
}

.wing-card:hover:not(.wing-disabled):not(.wing-revealed) {
  transform: translateY(-10px);
  filter: drop-shadow(0 10px 20px var(--color-cosmic-glow));
}

.wing-card.wing-selected {
  transform: translateY(0px);
  filter: drop-shadow(0 10px 20px var(--color-cosmic-glow));
}

.wing-card.wing-revealed:not(.wing-selected) {
  transform: translateY(0px);
  filter: drop-shadow(0 5px 10px var(--color-cosmic-glow));
}

.wing-inner {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  transition: transform var(--transition-duration) var(--transition-easing);
}

.wing-revealed .wing-inner {
  transform: rotateY(180deg);
}

.card-front,
.card-back {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: box-shadow 0.2s ease;
  overflow: hidden;
}

/* Card Front - Mystical Design */
.card-front {
  background: linear-gradient(135deg, 
    rgba(46, 167, 224, 0.1) 0%, 
    rgba(0, 168, 181, 0.1) 50%, 
    rgba(26, 26, 46, 0.2) 100%);
  border: 2px solid var(--color-cosmic-teal);
  box-shadow: 
    0 8px 32px rgba(0, 168, 181, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.card-front::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--color-cosmic-blue), var(--color-cosmic-teal), var(--color-cosmic-blue));
  border-radius: 16px;
  z-index: -1;
  animation: borderGlow 2s ease-in-out infinite alternate;
}

@keyframes borderGlow {
  from {
    opacity: 0.1;
    filter: blur(0px);
  }
  to {
    opacity: 0.5;
    filter: blur(1px);
  }
}

/* Card Back */
.card-back {
  background: linear-gradient(135deg, 
    var(--color-cosmic-blue) 0%, 
    var(--color-cosmic-teal) 100%);
  color: white;
  transform: rotateY(180deg);
  padding: 1rem;
  text-align: center;
  box-shadow: 
    0 8px 32px rgba(46, 167, 224, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Result-specific cosmic styles */
.result-daikichi .card-back { 
  background: linear-gradient(135deg, #00a8b5 0%, #2ea7e0 100%);
}
.result-kichi .card-back { 
  background: linear-gradient(135deg, #2ea7e0 0%, #00a8b5 100%);
}
.result-shokichi .card-back { 
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(46, 167, 224, 0.3) 100%);
}
.result-tonkotsu .card-back { 
  background: linear-gradient(135deg, #2ea7e0 0%, #8b4513 50%, #00a8b5 100%);
}

/* Cosmic Icons */
.wing-icon {
  filter: drop-shadow(0 0 10px var(--color-cosmic-glow));
  color: var(--color-cosmic-blue);
}

.result-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Button Effects */
.cosmic-btn-primary {
  background: linear-gradient(135deg, var(--color-cosmic-blue) 0%, var(--color-cosmic-teal) 100%);
  border: 1px solid var(--color-cosmic-teal);
  box-shadow: 0 4px 15px var(--color-cosmic-glow);
  position: relative;
  overflow: hidden;
}

.cosmic-btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.cosmic-btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-cosmic-teal) 0%, var(--color-cosmic-blue) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--color-cosmic-glow);
}

.cosmic-btn-primary:hover::before {
  left: 100%;
}

.cosmic-btn-secondary {
  background: transparent;
  color: var(--color-cosmic-blue);
  border: 2px solid var(--color-cosmic-blue);
  backdrop-filter: blur(10px);
}

.cosmic-btn-secondary:hover:not(:disabled) {
  background: var(--color-cosmic-blue);
  color: white;
  box-shadow: 0 4px 15px var(--color-cosmic-glow);
}

/* Cosmic Text Effects */
.cosmic-title {
  background: linear-gradient(135deg, var(--color-cosmic-blue) 0%, var(--color-cosmic-teal) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 20px var(--color-cosmic-glow);
}

/* Mobile Optimizations */
@media only screen and (max-width: 1024px) {
  .cosmic-background::before {
    background-attachment: scroll;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
  
  .wing-inner {
    transition: none;
  }
  
  .wing-revealed .wing-inner {
    transform: none;
  }
  
  .card-back {
    display: none;
  }
  
  .wing-revealed .card-back {
    display: flex;
  }
  
  .wing-card:hover:not(.wing-disabled):not(.wing-revealed) {
    transform: none;
  }
  
  .wing-card.wing-selected {
    transform: translateY(-10px) !important;
  }
  
  .wing-card.wing-revealed:not(.wing-selected) {
    transform: translateY(-5px) !important;
  }
}