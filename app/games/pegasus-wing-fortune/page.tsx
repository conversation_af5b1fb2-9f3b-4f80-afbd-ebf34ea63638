import { Metadata } from 'next';
import PegasusGame from './components/PegasusGame';
import './styles/cosmic.css';

export const metadata: Metadata = {
  title: 'ペガサスの羽みくじ | 株式会社スペースカウボーイ',
  description: '羽を1枚選んで運勢をチェック！宇宙感あふれる神秘的なおみくじゲーム。5枚の羽から1つを選んで、あなたの運勢を占ってみませんか？',
  openGraph: {
    title: 'ペガサスの羽みくじ | 株式会社スペースカウボーイ',
    description: '羽を1枚選んで運勢をチェック！宇宙感あふれる神秘的なおみくじゲーム。5枚の羽から1つを選んで、あなたの運勢を占ってみませんか？',
    url: 'https://www.space-cowboy.jp/games/pegasus-wing-fortune',
    siteName: '株式会社スペースカウボーイ',
    type: 'website',
    images: [
      {
        url: '/img/pegasus-fortune.webp',
        width: 1200,
        height: 630,
        alt: 'ペガサスの羽みくじ',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ペガサスの羽みくじ | 株式会社スペースカウボーイ',
    description: '羽を1枚選んで運勢をチェック！宇宙感あふれる神秘的なおみくじゲーム。',
    images: ['/img/pegasus-fortune.webp'],
  },
  keywords: ['おみくじ', '占い', 'ゲーム', 'ペガサス', '宇宙', 'インタラクティブ', 'スペースカウボーイ'],
  authors: [{ name: '株式会社スペースカウボーイ' }],
  robots: {
    index: true,
    follow: true,
  },
};

export default function PegasusWingFortunePage() {
  return (
    <main className="cosmic-theme cosmic-background min-h-screen bg-black text-white overflow-x-hidden">
      <PegasusGame />
    </main>
  );
}