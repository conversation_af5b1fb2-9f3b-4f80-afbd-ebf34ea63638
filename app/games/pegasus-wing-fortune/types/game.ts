// ペガサスの羽みくじゲーム型定義

export type ResultKey = 'DAIKICHI' | 'KICHI' | 'CHUKICHI' | 'TONKOTSU' | 'SHOKICHI';

export type GamePhase = 'IDLE' | 'PICKED' | 'REVEAL_ALL' | 'COMPLETE';

export interface GameResult {
  name: string;
  weight: number;
  message: string;
  color: 'success' | 'primary' | 'neutral';
}

export interface GameConfig {
  WINGS_COUNT: number;
  RESULTS: Record<ResultKey, GameResult>;
  REVEAL_DELAY: number;
  ANIMATION_DELAY: number;
}

export interface Wing {
  key: ResultKey;
  name: string;
  weight: number;
  message: string;
  color: 'success' | 'primary' | 'neutral';
}

export interface GameStats {
  playCount: number;
  bestResult: ResultKey | null;
}

export interface GameState {
  phase: GamePhase;
  wings: Wing[];
  selectedWingIndex: number;
  stats: GameStats;
}

export interface ShareData {
  title: string;
  text: string;
  url: string;
}