'use client';

import { useCallback, useState } from 'react';
import { Wing, ShareData } from '../types/game';

export function useShareResult() {
  const [shareStatus, setShareStatus] = useState<'idle' | 'sharing' | 'copied' | 'error'>('idle');

  const shareResult = useCallback(async (result: Wing | null) => {
    if (!result) return;

    const shareData: ShareData = {
      title: 'ペガサスの羽みくじ',
      text: `${result.name}が出ました！${result.message}`,
      url: typeof window !== 'undefined' ? window.location.href : ''
    };

    setShareStatus('sharing');

    try {
      // ネイティブシェアAPIが利用可能かチェック
      if (typeof navigator !== 'undefined' && navigator.share && navigator.canShare(shareData)) {
        await navigator.share(shareData);
        setShareStatus('idle');
      } else {
        // フォールバック：クリップボードにコピー
        const shareText = `${shareData.title}\n${shareData.text}\n${shareData.url}`;
        
        if (typeof navigator !== 'undefined' && navigator.clipboard) {
          await navigator.clipboard.writeText(shareText);
          setShareStatus('copied');
          
          // 2秒後にステータスをリセット
          setTimeout(() => {
            setShareStatus('idle');
          }, 2000);
        } else {
          throw new Error('Share and clipboard APIs not available');
        }
      }
    } catch (error) {
      console.warn('Share failed:', error);
      setShareStatus('error');
      
      // エラーステータスを2秒後にリセット
      setTimeout(() => {
        setShareStatus('idle');
      }, 2000);
    }
  }, []);

  const getShareButtonText = useCallback(() => {
    switch (shareStatus) {
      case 'sharing':
        return 'シェア中...';
      case 'copied':
        return 'コピー済み！';
      case 'error':
        return 'エラー';
      default:
        return 'シェア';
    }
  }, [shareStatus]);

  return {
    shareResult,
    shareStatus,
    getShareButtonText,
    isSharing: shareStatus === 'sharing'
  };
}