'use client';

import { useEffect } from 'react';
import { GameStats } from '../types/game';

const STORAGE_KEY = 'pegasus-wing-stats';

export function useGameStats(
  stats: GameStats,
  onStatsUpdate: (stats: GameStats) => void
) {
  // 統計をローカルストレージから読み込み
  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsedStats = JSON.parse(saved) as GameStats;
        onStatsUpdate({
          playCount: parsedStats.playCount || 0,
          bestResult: parsedStats.bestResult || null
        });
      }
    } catch (error) {
      console.warn('Failed to load stats:', error);
    }
  }, [onStatsUpdate]);

  // 統計をローカルストレージに保存
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(stats));
    } catch (error) {
      console.warn('Failed to save stats:', error);
    }
  }, [stats]);

  return {
    saveStats: () => {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(stats));
      } catch (error) {
        console.warn('Failed to save stats:', error);
      }
    },
    
    loadStats: (): GameStats | null => {
      try {
        const saved = localStorage.getItem(STORAGE_KEY);
        if (saved) {
          return JSON.parse(saved) as GameStats;
        }
      } catch (error) {
        console.warn('Failed to load stats:', error);
      }
      return null;
    },
    
    clearStats: () => {
      try {
        localStorage.removeItem(STORAGE_KEY);
        onStatsUpdate({
          playCount: 0,
          bestResult: null
        });
      } catch (error) {
        console.warn('Failed to clear stats:', error);
      }
    }
  };
}