'use client';

import { useState, useCallback, useEffect } from 'react';
import { GameState, GameConfig, Wing, ResultKey } from '../types/game';

// ゲーム設定（元のCONFIGから移植）
const CONFIG: GameConfig = {
  WINGS_COUNT: 5,
  RESULTS: {
    DAIKICHI: { name: '大吉', weight: 0.20, message: '最高の一日になりそう', color: 'success' },
    KICHI: { name: '吉', weight: 0.20, message: '良い流れに乗れそう', color: 'primary' },
    CHUKICHI: { name: '中吉', weight: 0.20, message: '良い流れに期待◎', color: 'neutral' },
    TONKOTSU: { name: '豚骨大吉', weight: 0.20, message: 'ラーメン食べたら運気アップ！', color: 'neutral' },
    SHOKICHI: { name: '小吉', weight: 0.20, message: '肩の力を抜いて◎', color: 'neutral' }
  },
  REVEAL_DELAY: 3000,
  ANIMATION_DELAY: 500
};

// セキュアランダム生成関数
function getSecureRandom(): number {
  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
    const array = new Uint32Array(1);
    window.crypto.getRandomValues(array);
    return array[0] / (0xffffffff + 1);
  }
  return Math.random();
}

// 配列をシャッフル
function shuffle<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(getSecureRandom() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// 結果プール生成（5つの異なる結果を必ず1つずつ）
function generateResultPool(): Wing[] {
  const pool: Wing[] = [];
  const resultKeys = Object.keys(CONFIG.RESULTS) as ResultKey[];
  
  for (let i = 0; i < CONFIG.WINGS_COUNT; i++) {
    const resultKey = resultKeys[i];
    pool.push({
      key: resultKey,
      ...CONFIG.RESULTS[resultKey]
    });
  }
  
  return shuffle(pool);
}

export function useGameLogic() {
  const [gameState, setGameState] = useState<GameState>({
    phase: 'IDLE',
    wings: [], // 空の配列で初期化
    selectedWingIndex: -1,
    stats: {
      playCount: 0,
      bestResult: null
    }
  });

  // クライアント側でのみ初期化（Hydration問題解決）
  useEffect(() => {
    setGameState(prev => ({
      ...prev,
      wings: generateResultPool()
    }));
  }, []);

  // 初期化
  const initializeGame = useCallback(() => {
    setGameState(prev => ({
      ...prev,
      phase: 'IDLE',
      wings: generateResultPool(),
      selectedWingIndex: -1
    }));
  }, []);

  // 最高結果を更新
  const updateBestResult = useCallback((newResult: ResultKey, currentBest: ResultKey | null): ResultKey => {
    const resultRanking: ResultKey[] = ['DAIKICHI', 'KICHI', 'CHUKICHI', 'SHOKICHI', 'TONKOTSU'];
    
    if (!currentBest || resultRanking.indexOf(newResult) < resultRanking.indexOf(currentBest)) {
      return newResult;
    }
    return currentBest;
  }, []);

  // 羽を選択
  const revealWing = useCallback((wingIndex: number) => {
    if (gameState.phase !== 'IDLE') return;

    const result = gameState.wings[wingIndex];
    if (!result) return;

    // 選択されたカードのみを即座にめくる
    setGameState(prev => ({
      ...prev,
      phase: 'PICKED',
      selectedWingIndex: wingIndex,
      stats: {
        ...prev.stats,
        playCount: prev.stats.playCount + 1,
        bestResult: updateBestResult(result.key, prev.stats.bestResult)
      }
    }));

    // 2秒後に残りの全ての羽を開示
    setTimeout(() => {
      setGameState(prev => ({
        ...prev,
        phase: 'REVEAL_ALL'
      }));

      // アニメーション完了後にゲーム完了状態に
      setTimeout(() => {
        setGameState(prev => ({
          ...prev,
          phase: 'COMPLETE'
        }));
      }, CONFIG.ANIMATION_DELAY);

    }, 2000); // 2秒に変更

  }, [gameState.phase, gameState.wings, updateBestResult]);

  // ゲームリセット
  const resetGame = useCallback(() => {
    initializeGame();
  }, [initializeGame]);

  // 選択された結果を取得
  const getSelectedResult = useCallback((): Wing | null => {
    if (gameState.selectedWingIndex >= 0 && gameState.selectedWingIndex < gameState.wings.length) {
      return gameState.wings[gameState.selectedWingIndex];
    }
    return null;
  }, [gameState.selectedWingIndex, gameState.wings]);

  return {
    gameState,
    CONFIG,
    revealWing,
    resetGame,
    initializeGame,
    getSelectedResult
  };
}