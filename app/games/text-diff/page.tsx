import { Metadata } from 'next';
import CreativeToolLayout from '../../../components/CreativeToolLayout';
import TextDiffTool from '../../../components/TextDiffTool';

export const metadata: Metadata = {
  title: 'テキスト比較・差分ツール - 無料で文章の違いを可視化 | 株式会社スペースカウボーイ',
  description: '無料のテキスト比較ツールで2つの文章の差分を瞬時に表示。追加・削除・変更部分をカラーハイライト。校正・レビュー作業を効率化。日本語完全対応。',
  keywords: ['テキスト比較', '差分チェック', '文章比較', '校正ツール', 'diff', '無料', 'レビュー', '文書管理', '日本語対応'],
  openGraph: {
    title: 'テキスト比較・差分ツール - 無料で文章の違いを可視化',
    description: '無料のテキスト比較ツールで2つの文章の差分を瞬時に表示。追加・削除・変更部分をカラーハイライト。校正・レビュー作業を効率化。',
    url: 'https://www.space-cowboy.jp/games/text-diff/',
    type: 'website',
    images: [
      {
        url: 'https://www.space-cowboy.jp/img/utility_04.jpg',
        width: 1200,
        height: 630,
        alt: 'テキスト比較ツールのプレビュー',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@cutkey5',
    title: 'テキスト比較・差分ツール - 無料で文章の違いを可視化',
    description: '無料のテキスト比較ツールで2つの文章の差分を瞬時に表示。校正・レビュー作業を効率化。',
  },
  alternates: {
    canonical: 'https://www.space-cowboy.jp/games/text-diff/',
  },
};

const toolUsageSteps = [
  {
    icon: '📝',
    title: '1. テキスト入力',
    description: '比較したい2つのテキストをそれぞれの入力欄に貼り付け'
  },
  {
    icon: '🔍',
    title: '2. 比較実行',
    description: '「テキストを比較」ボタンをクリックして差分を計算'
  },
  {
    icon: '🎨',
    title: '3. 結果確認',
    description: '追加部分は緑、削除部分は赤でハイライト表示'
  }
];

export default function TextDiffPage() {
  return (
    <CreativeToolLayout
      title="テキスト比較ツール"
      subtitle="2つのテキストの差分をハイライト表示"
      description="2つのテキストを比較して差分を視覚的に表示します。追加部分は緑、削除部分は赤でハイライトされ、文章の変更点を素早く把握できます。"
      currentToolId="text-diff"
      toolUsageSteps={toolUsageSteps}
    >
      <TextDiffTool />
    </CreativeToolLayout>
  );
}