import { Metadata } from 'next';
import Navigation from '../../components/Navigation';
import GamesContent from './GamesContent';
import CTASection from '../../components/CTASection';
import Footer from '../../components/Footer';

export const metadata: Metadata = {
  title: '🎮LAB | 株式会社スペースカウボーイ',
  description: 'ゲームやインタラクティブな仕掛け、そしてクリエイティブを支援するツール群を通じて、想いやメッセージを"体験"として届けます。🎮LABは「参加」や「共感」を生むコミュニケーションをデザインする実験室です。',
  openGraph: {
    title: '🎮LAB | 株式会社スペースカウボーイ',
    description: 'ゲームやインタラクティブな仕掛け、そしてクリエイティブを支援するツール群を通じて、想いやメッセージを"体験"として届けます。🎮LABは「参加」や「共感」を生むコミュニケーションをデザインする実験室です。',
    url: 'https://www.space-cowboy.jp/games',
  },
};

export default function GamesPage() {
  return (
    <main className="bg-gray-900/0 text-white games-page">
      <Navigation />
      
      {/* パララックス背景 */}
      <div className="parallax-bg" id="parallax-bg"></div>
      
      {/* 背景を暗くするオーバーレイ */}
      <div className="fixed inset-0 bg-black/30 z-0"></div>

      {/* メインコンテンツ */}
      <div className="parallax-wrapper">
        <section className="min-h-screen pt-32 md:pt-48 relative z-10">
          <div className="max-w-6xl mx-auto px-6 md:px-12">
            {/* ページタイトル */}
            <div className="mb-24 animate-fadeInUpSlow">
              <h1 className="text-5xl md:text-7xl montserrat-bold text-white mb-8">🎮 LAB</h1>
              <p className="text-xl md:text-2xl text-blue-300 font-light mb-8">
                インタラクションあふれる体験とツール
              </p>
              <p className="text-base md:text-lg text-gray-300 leading-loose max-w-4xl">
                ゲームやインタラクティブな仕掛け、そしてクリエイティブを支援するツール群を通じて、想いやメッセージを&quot;体験&quot;として届けます。スペースカウボーイは「参加」や「共感」を生むコミュニケーションをデザインします。
              </p>
            </div>
          </div>

          {/* ゲーム一覧エリア */}
          <div className="w-full bg-gray-900/80">
            <div className="max-w-6xl mx-auto px-6 md:px-12 py-12">
              <GamesContent />
            </div>
          </div>

        </section>
      </div>

      <CTASection />

      <Footer />
    </main>
  );
}