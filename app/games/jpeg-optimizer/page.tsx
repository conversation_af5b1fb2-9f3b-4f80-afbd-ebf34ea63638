import { Metadata } from 'next';
import CreativeToolLayout from '../../../components/CreativeToolLayout';
import JpegOptimizer from '../../../components/JpegOptimizer';

export const metadata: Metadata = {
  title: 'JPEG圧縮・最適化ツール - 無料で50KB以下に自動圧縮 | 株式会社スペースカウボーイ',
  description: '無料のJPEG圧縮ツールでファイルサイズを50KB以下に自動最適化。Web表示速度向上、メール添付に最適。品質を保ったままファイルサイズを大幅削減。',
  keywords: ['JPEG圧縮', '画像圧縮', '画像最適化', '50KB', 'Web最適化', '無料ツール', 'ファイルサイズ削減', 'オンライン'],
  openGraph: {
    title: 'JPEG圧縮・最適化ツール - 無料で50KB以下に自動圧縮',
    description: '無料のJPEG圧縮ツールでファイルサイズを50KB以下に自動最適化。Web表示速度向上、メール添付に最適。',
    url: 'https://www.space-cowboy.jp/games/jpeg-optimizer/',
    type: 'website',
    images: [
      {
        url: 'https://www.space-cowboy.jp/img/utility_01.jpg',
        width: 1200,
        height: 630,
        alt: 'JPEG最適化ツールのプレビュー',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@cutkey5',
    title: 'JPEG圧縮・最適化ツール - 無料で50KB以下に自動圧縮',
    description: '無料のJPEG圧縮ツールでファイルサイズを50KB以下に自動最適化。Web表示速度向上に最適。',
  },
  alternates: {
    canonical: 'https://www.space-cowboy.jp/games/jpeg-optimizer/',
  },
};

const toolUsageSteps = [
  {
    icon: '📁',
    title: '1. ファイル選択',
    description: 'JPEGファイルをドラッグ&ドロップまたはクリックして選択'
  },
  {
    icon: '⚙️',
    title: '2. 自動最適化',
    description: '最大幅1000px、50KB以下に自動でリサイズ・圧縮'
  },
  {
    icon: '💾',
    title: '3. ダウンロード',
    description: '最適化された画像をダウンロード'
  }
];

export default function JpegOptimizerPage() {
  return (
    <CreativeToolLayout
      title="JPEG最適化ツール"
      subtitle="画像を50KB以下に自動圧縮"
      description="JPEGファイルを自動でリサイズ・圧縮。最大幅1000px、ファイルサイズ50KB以下に最適化します。Web掲載用の画像処理に最適なツールです。"
      currentToolId="jpeg-optimizer"
      toolUsageSteps={toolUsageSteps}
    >
      <JpegOptimizer />
    </CreativeToolLayout>
  );
}