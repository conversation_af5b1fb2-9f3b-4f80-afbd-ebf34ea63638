import Link from 'next/link';
import Image from 'next/image';
import { ArticleMetadata } from '@/lib/articles/types';

interface ArticleCardProps {
  article: ArticleMetadata;
  className?: string;
  featured?: boolean;
}

export default function ArticleCard({ article, className = '', featured = false }: ArticleCardProps) {
  const cardClass = featured 
    ? 'bg-gradient-to-br from-blue-900/30 to-purple-900/30 border-blue-500/50' 
    : 'bg-gray-800/50 border-gray-700/30';

  return (
    <article className={`
      ${cardClass}
      rounded-2xl overflow-hidden hover:bg-gray-800/70 
      transition-all duration-300 hover:transform hover:scale-105 
      backdrop-blur-sm border group
      ${className}
    `}>
      <Link href={`/articles/${article.slug}`} className="block">
        {/* サムネイル画像 */}
        <div className="relative aspect-video overflow-hidden">
          <Image
            src={article.thumbnail}
            alt={article.title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          
          {/* カテゴリーバッジ */}
          <div className="absolute top-4 left-4">
            <span className="bg-blue-500/90 text-white text-xs font-semibold px-3 py-1 rounded-full uppercase backdrop-blur-sm">
              {article.category}
            </span>
          </div>

          {/* 注目記事バッジ */}
          {featured && (
            <div className="absolute top-4 right-4">
              <span className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-semibold px-3 py-1 rounded-full flex items-center">
                ⭐ Featured
              </span>
            </div>
          )}
        </div>

        {/* コンテンツ */}
        <div className="p-6">
          {/* メタ情報 */}
          <div className="flex items-center gap-3 text-xs text-gray-400 mb-3">
            <time dateTime={article.date}>
              {new Date(article.date).toLocaleDateString('ja-JP', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </time>
            <span>•</span>
            <span>{article.readTime}で読了</span>
            <span>•</span>
            <span>{article.author}</span>
          </div>

          {/* タイトル */}
          <h3 className="text-xl font-bold text-white mb-3 line-clamp-2 group-hover:text-blue-300 transition-colors">
            {article.title}
          </h3>



          {/* 会社情報（存在する場合） */}
          {article.company && (
            <div className="flex items-center gap-3 mb-4 p-3 bg-gray-700/30 rounded-lg">
              <Image
                src={article.company.logo}
                alt={`${article.company.name} logo`}
                width={32}
                height={32}
                className="rounded"
              />
              <div>
                <p className="text-white text-sm font-semibold">{article.company.name}</p>
                <p className="text-gray-400 text-xs">{article.company.industry}</p>
              </div>
            </div>
          )}


          {/* 読み進めるリンク */}
          <div className="flex items-center justify-between">
            <div className="flex items-center text-blue-400 text-sm font-medium group-hover:text-blue-300 transition-colors">
              記事を読む
              <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
            
            {/* 読了時間 */}
            <div className="text-xs text-gray-500 flex items-center">
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {article.readTime}
            </div>
          </div>
        </div>
      </Link>
    </article>
  );
}