import Image from 'next/image';
import { Company } from '@/lib/articles/types';

interface CompanyProfileProps {
  company: Company;
  challenge?: string;
  result?: string;
  className?: string;
}

export default function CompanyProfile({ 
  company, 
  challenge, 
  result, 
  className = '' 
}: CompanyProfileProps) {
  return (
    <div className={`
      bg-gradient-to-br from-gray-800/60 to-gray-900/60 
      border border-gray-600/50 rounded-xl p-6 my-8 
      backdrop-blur-sm shadow-lg
      ${className}
    `}>
      {/* 会社情報ヘッダー */}
      <div className="flex items-center gap-4 mb-6">
        <div className="relative">
          <Image
            src={company.logo}
            alt={`${company.name} ロゴ`}
            width={64}
            height={64}
            className="rounded-lg border-2 border-gray-600 bg-white p-1"
          />
        </div>
        
        <div className="flex-1">
          <h3 className="text-2xl font-bold text-white mb-2 flex items-center">
            <svg className="w-6 h-6 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            {company.name}
          </h3>
          
          <div className="flex items-center text-gray-300">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V8m8 0V6a2 2 0 00-2-2H10a2 2 0 00-2 2v2m0 0h8" />
            </svg>
            <span className="text-sm font-medium">{company.industry}</span>
          </div>
        </div>
      </div>

      {/* 課題と成果のセクション */}
      {(challenge || result) && (
        <div className="space-y-4">
          {challenge && (
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
              <h4 className="text-red-300 font-semibold mb-3 flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                解決したい課題
              </h4>
              <p className="text-gray-300 leading-relaxed">{challenge}</p>
            </div>
          )}
          
          {result && (
            <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
              <h4 className="text-green-300 font-semibold mb-3 flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                実現できた成果
              </h4>
              <p className="text-gray-300 leading-relaxed">{result}</p>
            </div>
          )}
        </div>
      )}

      {/* フッター */}
      <div className="mt-4 pt-4 border-t border-gray-600/50">
        <p className="text-xs text-gray-400 text-center">
          この事例は {company.name} の協力により作成されました
        </p>
      </div>
    </div>
  );
}