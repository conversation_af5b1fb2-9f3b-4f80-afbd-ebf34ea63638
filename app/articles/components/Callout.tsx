import { ReactNode } from 'react';

interface CalloutProps {
  children: ReactNode;
  type?: 'info' | 'warning' | 'success' | 'error' | 'tip';
  title?: string;
  className?: string;
}

const calloutStyles = {
  info: {
    container: 'bg-blue-900/30 border-blue-500/50 text-blue-200',
    icon: '💡',
    title: 'text-blue-300'
  },
  warning: {
    container: 'bg-yellow-900/30 border-yellow-500/50 text-yellow-200',
    icon: '⚠️',
    title: 'text-yellow-300'
  },
  success: {
    container: 'bg-green-900/30 border-green-500/50 text-green-200',
    icon: '✅',
    title: 'text-green-300'
  },
  error: {
    container: 'bg-red-900/30 border-red-500/50 text-red-200',
    icon: '❌',
    title: 'text-red-300'
  },
  tip: {
    container: 'bg-purple-900/30 border-purple-500/50 text-purple-200',
    icon: '💡',
    title: 'text-purple-300'
  }
};

export default function Callout({ 
  children, 
  type = 'info', 
  title,
  className = '' 
}: CalloutProps) {
  const styles = calloutStyles[type];

  return (
    <div className={`
      ${styles.container}
      border rounded-lg p-6 my-6 backdrop-blur-sm
      ${className}
    `}>
      {title && (
        <div className={`flex items-center gap-2 mb-3 ${styles.title} font-semibold`}>
          <span className="text-lg">{styles.icon}</span>
          {title}
        </div>
      )}
      
      <div className="prose prose-sm max-w-none">
        {children}
      </div>
    </div>
  );
}