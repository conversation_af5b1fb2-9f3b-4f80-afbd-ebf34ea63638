import { ArticleListItem } from '@/lib/articles/types';
import ArticleCard from './ArticleCard';
import Link from 'next/link';

interface RelatedArticlesProps {
  articles: ArticleListItem[];
  title?: string;
  className?: string;
}

export default function RelatedArticles({ 
  articles, 
  title = '関連記事', 
  className = '' 
}: RelatedArticlesProps) {
  if (articles.length === 0) {
    return null;
  }

  return (
    <section className={`${className}`}>
      <div className="flex items-center justify-between mb-8">
        <h2 className="text-3xl font-bold text-white flex items-center">
          <svg className="w-8 h-8 mr-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
          </svg>
          {title}
        </h2>
        
        {/* 記事数 */}
        <span className="text-sm text-gray-400 bg-gray-800 px-3 py-1 rounded-full">
          {articles.length}記事
        </span>
      </div>

      {/* 関連記事グリッド */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {articles.map((article) => (
          <ArticleCard
            key={article.metadata.slug}
            article={article.metadata}
            className="h-full"
          />
        ))}
      </div>

      {/* さらに記事を見る */}
      {articles.length >= 3 && (
        <div className="text-center mt-8">
          <Link
            href="/articles"
            className="inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors font-medium"
          >
            さらに記事を見る
            <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      )}
    </section>
  );
}