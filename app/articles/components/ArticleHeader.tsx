import Image from 'next/image';
import Link from 'next/link';
import { ArticleMetadata } from '@/lib/articles/types';

interface ArticleHeaderProps {
  article: ArticleMetadata;
}

export default function ArticleHeader({ article }: ArticleHeaderProps) {
  const formattedDate = new Date(article.date).toLocaleDateString('ja-JP', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <header className="mb-12">
      {/* パンくずナビ */}
      <nav className="flex items-center text-sm text-gray-400 mb-8">
        <Link href="/articles" className="hover:text-blue-400 transition-colors">
          記事一覧
        </Link>
        <span className="mx-2">/</span>
        <Link 
          href={`/articles?category=${article.category}`}
          className="hover:text-blue-400 transition-colors"
        >
          {article.category}
        </Link>
        <span className="mx-2">/</span>
        <span className="text-gray-500">現在の記事</span>
      </nav>

      {/* メタ情報 */}
      <div className="flex items-center gap-4 text-sm text-gray-400 mb-6">
        <time dateTime={article.date}>{formattedDate}</time>
        <span>•</span>
        <span>{article.readTime}で読了</span>
        <span>•</span>
        <span>{article.author}</span>
        
        {/* カテゴリーバッジ */}
        <Link 
          href={`/articles?category=${article.category}`}
          className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full hover:bg-blue-500/30 transition-colors text-xs font-semibold uppercase"
        >
          {article.category}
        </Link>
      </div>

      {/* タイトル・サブタイトル */}
      <div className="mb-8">
        <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
          {article.title}
        </h1>
        

      </div>

      {/* 会社情報（存在する場合） */}
      {article.company && (
        <div className="bg-gray-800/50 rounded-xl p-6 mb-8 border border-gray-700/30 backdrop-blur-sm">
          <h2 className="text-lg font-semibold text-white mb-4 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            事例企業情報
          </h2>
          
          <div className="flex items-start gap-4">
            <Image
              src={article.company.logo}
              alt={`${article.company.name} ロゴ`}
              width={60}
              height={60}
              className="rounded-lg border border-gray-600"
            />
            
            <div className="flex-1">
              <h3 className="text-xl font-bold text-white mb-2">{article.company.name}</h3>
              <p className="text-gray-400 mb-3">{article.company.industry}</p>
              
              {/* 課題と成果 */}
              {(article.challenge || article.result) && (
                <div className="grid md:grid-cols-2 gap-4 mt-4">
                  {article.challenge && (
                    <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
                      <h4 className="text-red-300 font-semibold mb-2 flex items-center text-sm">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        課題
                      </h4>
                      <p className="text-gray-300 text-sm">{article.challenge}</p>
                    </div>
                  )}
                  
                  {article.result && (
                    <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
                      <h4 className="text-green-300 font-semibold mb-2 flex items-center text-sm">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        成果
                      </h4>
                      <p className="text-gray-300 text-sm">{article.result}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}


      {/* 区切り線 */}
      <div className="border-t border-gray-700 pt-8">
        {/* 記事本文への導入 */}
        <p className="text-gray-400 text-sm">
          この記事では、{article.company?.name || '企業'}の事例を通じて、クリエイティブ制作における具体的な取り組みと成果をご紹介します。
        </p>
      </div>
    </header>
  );
}