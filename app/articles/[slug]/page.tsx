import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { MDXRemote } from 'next-mdx-remote/rsc';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import Navigation from '../../../components/Navigation';
import Footer from '../../../components/Footer';
import ShareButtons from '../components/ShareButtons';
import Image from 'next/image';
import { getArticleBySlug, getAllArticleSlugs } from '@/lib/articles/mdx';
import { getMDXComponents } from '../../../components/mdx-components';
import ProtectedArticle from './ProtectedArticle';
import { getRelatedArticles } from '@/lib/articles/mdx';
import ContentCard from '../../../components/ContentCard';
import MiniContactForm from '../../../components/MiniContactForm';

interface ArticlePageProps {
  params: Promise<{
    slug: string;
  }>;
}

// 静的パスを生成
export async function generateStaticParams() {
  const slugs = await getAllArticleSlugs();
  
  return slugs.map((slug) => ({
    slug,
  }));
}

// メタデータを生成
export async function generateMetadata({ params }: ArticlePageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const article = await getArticleBySlug(resolvedParams.slug);
  
  if (!article) {
    return {
      title: '記事が見つかりません | 株式会社スペースカウボーイ',
    };
  }

  const { metadata } = article;
  
  return {
    title: `${metadata.title} | 株式会社スペースカウボーイ`,
    description: metadata.metaDescription || metadata.title,
    keywords: metadata.keywords,
    openGraph: {
      title: metadata.title,
      description: metadata.metaDescription || metadata.title,
      url: `https://www.space-cowboy.jp/articles/${metadata.slug}`,
      type: 'article',
      publishedTime: metadata.date,
      authors: ['SpaceCowboy'],
      images: [
        {
          url: metadata.thumbnail,
          width: 1200,
          height: 630,
          alt: metadata.title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: metadata.title,
      description: metadata.metaDescription || metadata.title,
      images: [metadata.thumbnail],
    },
  };
}

export default async function ArticlePage({ params }: ArticlePageProps) {
  const resolvedParams = await params;
  const article = await getArticleBySlug(resolvedParams.slug);
  
  if (!article) {
    notFound();
  }

  const { metadata, content } = article;
  const relatedArticles = await getRelatedArticles({ 
    currentSlug: resolvedParams.slug, 
    limit: 3 
  });

  return (
    <main className="bg-gray-900/10 text-white">
      <Navigation />
      
      <ProtectedArticle slug={resolvedParams.slug}>
        {/* パララックス背景 */}
        <div className="parallax-bg" id="parallax-bg"></div>

        {/* メインコンテンツ */}
        <div className="parallax-wrapper">
          <div className="min-h-screen pt-32 md:pt-48 relative z-10">
            <div className="max-w-7xl mx-auto px-0 md:px-0">
              {/* 記事エリア全体を不透明な背景で覆う */}
              <div className="bg-gray-900">
                {/* 2カラムレイアウト */}
                <div className="md:grid md:grid-cols-3 md:gap-8 p-4 md:p-8">
                {/* 左カラム: メインコンテンツ */}
                <div className="md:col-span-2">
                {/* 記事ヘッダー */}
                <header className="mb-8">
                  <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
                    {metadata.title}
                  </h1>
                  {/* 日付とタグ */}
                  <div className="flex flex-wrap items-center gap-4 mb-6">
                    <div className="text-sm text-gray-400">
                      {new Date(metadata.date).toLocaleDateString('ja-JP')}
                    </div>
                    
                  </div>
                  
                  {/* メイン画像 */}
                  <div className="mb-8">
                    <Image
                      src={metadata.thumbnail}
                      alt={metadata.title}
                      width={800}
                      height={450}
                      className="w-full rounded-lg"
                      style={{
                        maxWidth: '100%',
                        height: 'auto',
                      }}
                    />
                  </div>
                </header>

                {/* 企業情報セクション（課題・成果） */}
                {metadata.company && (metadata.challenge || metadata.result) && (
                  <div className="bg-gray-800 p-6 border border-gray-700 mb-8">
                    {metadata.challenge && (
                      <div className="mb-4">
                        <h5 className="text-sm font-bold text-blue-400 mb-2">📌 課題</h5>
                        <p className="text-gray-300 text-sm">{metadata.challenge}</p>
                      </div>
                    )}
                    
                    {metadata.result && (
                      <div>
                        <h5 className="text-sm font-bold text-green-400 mb-2">✨ 成果</h5>
                        <p className="text-gray-300 text-sm">{metadata.result}</p>
                      </div>
                    )}
                  </div>
                )}

                {/* 記事本文 */}
                <article className="prose prose-lg max-w-none text-gray-300 w-full overflow-x-hidden">
                  <MDXRemote
                    source={content}
                    options={{
                      mdxOptions: {
                        remarkPlugins: [remarkGfm],
                        rehypePlugins: [rehypeHighlight],
                      },
                    }}
                    components={getMDXComponents()}
                  />
                </article>
              </div>

              {/* 右カラム: サイドバー（PCのみ表示） */}
              <div className="md:col-span-1 mt-12 md:mt-0 hidden md:block">
                <div className="md:sticky md:top-24 space-y-6">
                  {/* 企業情報カード */}
                  {metadata.company && (
                    <div className="bg-gray-800 p-6 border border-gray-700">
                      <h3 className="text-sm font-bold text-gray-400 mb-4">COMPANY INFO</h3>
                      <div>
                        {metadata.company.logo && (
                          <div className="mb-3">
                            <Image
                              src={metadata.company.logo}
                              alt={`${metadata.company.name} ロゴ`}
                              width={300}
                              height={150}
                              className="rounded w-full h-auto"
                            />
                          </div>
                        )}
                        <h4 className="text-white font-semibold text-sm mb-2">{metadata.company.name}</h4>
                        <p className="text-gray-400 text-xs">{metadata.company.industry}</p>
                      </div>
                    </div>
                  )}
                  
                  {/* ミニお問い合わせフォーム */}
                  <div className="bg-gray-800 p-6 border border-gray-700">
                    <h3 className="text-sm font-bold text-gray-400 mb-4">QUICK CONTACT</h3>
                    <MiniContactForm 
                      articleTitle={metadata.title}
                      articleSlug={metadata.slug}
                    />
                  </div>
                  
                  {/* シェアボタン */}
                  <div className="bg-gray-800 p-6 border border-gray-700">
                    <h3 className="text-sm font-bold text-gray-400 mb-4">SHARE</h3>
                    <ShareButtons title={metadata.title} slug={metadata.slug} />
                  </div>
                </div>
              </div>
            </div>
            
            {/* 関連記事セクション */}
            {relatedArticles.length > 0 && (
              <div className="mt-16 p-4 md:p-8">
                <div>
                  <h2 className="text-xl font-bold text-white mb-6">関連記事</h2>
                  <div className="grid gap-6 md:grid-cols-3">
                    {relatedArticles.map((relatedArticle) => (
                      <ContentCard
                        key={relatedArticle.metadata.slug}
                        id={relatedArticle.metadata.slug}
                        date={new Date(relatedArticle.metadata.date).toLocaleDateString('ja-JP')}
                        title={relatedArticle.metadata.title}
                        image={relatedArticle.metadata.thumbnail}
                        link={{
                          url: `/articles/${relatedArticle.metadata.slug}`,
                          text: '記事を読む'
                        }}
                        type="article"
                      />
                    ))}
                  </div>
                </div>
              </div>
            )}
            
            {/* モバイル用企業情報（スマホのみ表示） */}
            {metadata.company && (
              <div className="md:hidden mt-12">
                <div className="bg-gray-800 p-6">
                  <h3 className="text-sm font-bold text-gray-400 mb-4">COMPANY INFO</h3>
                  <div className="flex gap-4 mb-4">
                    {metadata.company.logo && (
                      <div className="flex-shrink-0">
                        <Image
                          src={metadata.company.logo}
                          alt={`${metadata.company.name} ロゴ`}
                          width={120}
                          height={120}
                          className="rounded"
                        />
                      </div>
                    )}
                    <div className="flex-1">
                      <h4 className="text-white font-semibold mb-2">{metadata.company.name}</h4>
                      <p className="text-gray-400 text-sm">{metadata.company.industry}</p>
                    </div>
                  </div>
                  
                  {/* モバイル用ミニお問い合わせフォーム */}
                  <div className="mt-6 pt-6 border-t border-gray-600">
                    <h3 className="text-sm font-bold text-gray-400 mb-4">QUICK CONTACT</h3>
                    <MiniContactForm 
                      articleTitle={metadata.title}
                      articleSlug={metadata.slug}
                    />
                  </div>
                  
                  {/* モバイル用シェアボタン */}
                  <div className="mt-6 pt-6 border-t border-gray-600">
                    <h3 className="text-sm font-bold text-gray-400 mb-4">SHARE</h3>
                    <ShareButtons title={metadata.title} slug={metadata.slug} />
                  </div>
                </div>
              </div>
            )}
            </div>
          </div>
        </div>

        </div>
      </ProtectedArticle>

      <Footer />
    </main>
  );
}