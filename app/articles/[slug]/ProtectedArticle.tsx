'use client';

import { useState, ReactNode } from 'react';

interface ProtectedArticleProps {
  slug: string;
  children: ReactNode;
}

// パスワード保護が必要な記事のスラッグ
const PROTECTED_SLUGS = ['quando_01_q', 'quando_01', 'quando_02'];

export default function ProtectedArticle({ slug, children }: ProtectedArticleProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const isDevMode = process.env.NEXT_PUBLIC_DEV_MODE === 'true';
  const isProtected = PROTECTED_SLUGS.includes(slug);

  const handleLogin = () => {
    if (password === process.env.NEXT_PUBLIC_PASSWORD) {
      setIsAuthenticated(true);
      setError('');
    } else {
      setError('パスワードが正しくありません');
    }
  };

  // 開発モード、保護されていない記事、または認証済みの場合はそのまま表示
  if (isDevMode || !isProtected || isAuthenticated) {
    return <>{children}</>;
  }

  // パスワード入力画面
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900 py-8">
      <div className="bg-gray-800 p-6 rounded-lg max-w-sm w-full mx-4">
        <h2 className="text-xl font-bold text-white mb-4 text-center">認証が必要です</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              パスワード
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyDown={(e) => e.key === 'Enter' && handleLogin()}
              placeholder="パスワードを入力"
              autoComplete="off"
            />
          </div>
          
          {error && (
            <p className="text-red-400 text-sm text-center">{error}</p>
          )}
          
          <button
            onClick={handleLogin}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
          >
            ログイン
          </button>
        </div>
      </div>
    </div>
  );
}