import { Metadata } from 'next';
import Navigation from '../../components/Navigation';
import Footer from '../../components/Footer';
import CTASection from '../../components/CTASection';
import ArticlesClientContent from './ArticlesClientContent';
import { getAllArticles } from '@/lib/articles/mdx';

export const metadata: Metadata = {
  title: '記事 | 株式会社スペースカウボーイ',
  description: '宇宙事業とクリエイティブに関する最新情報、技術解説、業界動向などをお届けします。',
  openGraph: {
    title: '記事 | 株式会社スペースカウボーイ',
    description: '宇宙事業とクリエイティブに関する最新情報、技術解説、業界動向などをお届けします。',
    url: 'https://www.space-cowboy.jp/articles',
  },
};

export default async function ArticlesPage() {
  // サーバーサイドでデータを取得
  const articles = await getAllArticles({ filters: { published: true }, sortBy: 'date', sortOrder: 'desc' });

  return (
    <main className="bg-gray-900/0 text-white articles-page">
      <Navigation />
      
      {/* パララックス背景 */}
      <div className="parallax-bg" id="parallax-bg"></div>
      
      {/* 背景を暗くするオーバーレイ */}
      <div className="fixed inset-0 bg-black/30 z-0"></div>

      {/* メインコンテンツ */}
      <div className="parallax-wrapper">
        <section className="min-h-screen pt-32 md:pt-48 relative z-10">
          <div className="max-w-6xl mx-auto px-6 md:px-12">
            {/* ページタイトル */}
            <div className="mb-24 animate-fadeInUpSlow">
              <h1 className="text-5xl md:text-7xl montserrat-bold text-white mb-8">ARTICLES</h1>
              <p className="text-xl md:text-2xl text-blue-300 font-light mb-8">
                クリエイティブに関するお役立ち情報
              </p>
              <p className="text-base md:text-lg text-gray-300 leading-loose max-w-4xl">
                クリエイティブに関するお役立ち情報などをお届けします。
              </p>
            </div>
          </div>

          {/* コンテンツエリア */}
          <ArticlesClientContent 
            initialArticles={articles}
          />

        </section>
      </div>

      <CTASection />
      <Footer />
    </main>
  );
}