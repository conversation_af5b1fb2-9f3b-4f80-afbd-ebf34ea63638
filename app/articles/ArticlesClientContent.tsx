'use client';

import { useState } from 'react';
import ContentCard from '../../components/ContentCard';
import { ArticleListItem } from '@/lib/articles/types';

interface ArticlesClientContentProps {
  initialArticles: ArticleListItem[];
}

export default function ArticlesClientContent({
  initialArticles
}: ArticlesClientContentProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showPasswordPrompt, setShowPasswordPrompt] = useState(false);
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const isDevMode = process.env.NEXT_PUBLIC_DEV_MODE === 'true';

  const handleRocketClick = () => {
    setIsAuthenticated(true);  // 常に認証済み状態にする
  };

  const handleLogin = () => {
    if (password === process.env.NEXT_PUBLIC_PASSWORD) {
      setIsAuthenticated(true);
      setShowPasswordPrompt(false);
      setError('');
    } else {
      setError('パスワードが正しくありません');
    }
  };

  const handleCancel = () => {
    setShowPasswordPrompt(false);
    setPassword('');
    setError('');
  };

  // 開発モードまたは認証済みの場合は記事一覧を表示
  if (isDevMode || isAuthenticated) {
    return (
      <div className="w-full bg-gray-900">
        <div className="max-w-6xl mx-auto px-6 md:px-12 py-24 space-y-32">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {initialArticles.map((article) => (
              <ContentCard
                key={article.metadata.slug}
                id={article.metadata.slug}
                date={article.metadata.date}
                title={article.metadata.title}
                image={article.metadata.thumbnail}
                link={{
                  url: `/articles/${article.metadata.slug}`,
                  text: '記事を読む',
                  external: false
                }}
                type="article"
              />
            ))}
          </div>
        </div>
      </div>
    );
  }

  // パスワード入力ダイアログ
  if (showPasswordPrompt) {
    return (
      <div className="w-full bg-gray-900 min-h-screen flex items-center justify-center">
        <div className="bg-gray-800 p-8 rounded-lg max-w-md w-full mx-4">
          <h2 className="text-2xl font-bold text-white mb-6 text-center">認証が必要です</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                パスワード
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                onKeyDown={(e) => e.key === 'Enter' && handleLogin()}
                placeholder="パスワードを入力"
                autoComplete="off"
              />
            </div>
            
            {error && (
              <p className="text-red-400 text-sm text-center">{error}</p>
            )}
            
            <div className="flex space-x-3">
              <button
                onClick={handleLogin}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                ログイン
              </button>
              <button
                onClick={handleCancel}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                キャンセル
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Coming Soon画面（未認証時）
  return (
    <div className="w-full bg-gray-900">
      <div className="max-w-6xl mx-auto px-6 md:px-12 py-24">
        <div className="text-center py-4">
          <p 
            className="text-6xl mb-6 cursor-pointer hover:scale-110 transition-transform" 
            id="rocket-emoji"
            onClick={handleRocketClick}
          >
            🚀
          </p>
          <p className="text-3xl font-bold mb-4 montserrat-bold">Coming Soon</p>
          <p className="text-lg text-gray-300">
            記事コンテンツは現在準備中です。<br />
            今しばらくお待ちください。
          </p>
        </div>
      </div>
    </div>
  );
}