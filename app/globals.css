@import "tailwindcss";

:root {
  --background: #0a0a0a;
  --foreground: #ededed;
}

body {
  font-family: var(--font-noto-sans-jp), 'Noto Sans JP', sans-serif;
  line-height: 1.6;
  color: #fff;
  background: var(--background);
  overflow-x: hidden;
}

html {
  scroll-behavior: smooth;
}

@layer components {
  .montserrat-regular {
    font-family: var(--font-montserrat), "Montserrat", sans-serif;
    font-weight: 400;
    font-style: normal;
  }

  .montserrat-bold {
    font-family: var(--font-montserrat), "Montserrat", sans-serif;
    font-weight: 600;
    font-style: normal;
  }
}

@layer utilities {
  /* Text truncation utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    line-clamp: 3;
  }

  /* スクロールバー非表示 */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Chrome, Safari */
  }

  /* Mobile-specific line clamp */
  @media (max-width: 640px) {
    .line-clamp-1,
    .line-clamp-2,
    .line-clamp-3 {
      word-break: break-word;
      hyphens: auto;
      text-overflow: ellipsis;
    }
  }
}

@layer components {
  /* CTAボタンスタイル */
  .hero-cta-button,
  .concept-cta-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 16px 32px;
    border-radius: 50px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    text-transform: none;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
  }

  .hero-cta-button:hover,
  .concept-cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  }

  .hero-cta-button:active,
  .concept-cta-button:active {
    transform: translateY(0);
  }

  .hero-cta-button::before,
  .concept-cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  .hero-cta-button:hover::before,
  .concept-cta-button:hover::before {
    left: 100%;
  }
}

/* パララックス背景 */
.parallax-bg {
    background-image: url('/img/universe.webp');
    background-size: cover;
    background-position: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    z-index: -1;
    opacity: 0.7;
    will-change: transform;
    animation: fadeIn 0.5s ease-in, zoomOut 5s ease-out;
}

/* サービスページ専用背景 */
.service-page .parallax-bg {
    background-image: url('/img/service.webp');
}

/* 会社概要ページ専用背景 */
.company-page .parallax-bg {
    background-image: url('/img/company_image.webp');
}

/* お問い合わせページ専用背景 */
.contact-page .parallax-bg {
    background-image: url('/img/contact_image.webp');
}

/* ゲームページ専用背景 */
.games-page .parallax-bg {
    background-image: url('/img/universe.webp');
}

/* 記事ページ専用背景 */
.articles-page .parallax-bg {
    background-image: url('/img/universe.webp');
}


/* デスクトップのみbackground-attachment: fixedを使用 */
@media (min-width: 1025px) {
    .parallax-bg {
        background-attachment: fixed;
        transform: none !important;
    }
}

/* iOS/モバイル対応 */
@supports (-webkit-touch-callout: none) {
    .parallax-bg {
        background-attachment: scroll;
        height: 120vh; /* スクロール時の余白対策 */
    }
}

/* 追加のiOS対策 */
@media only screen and (max-width: 1024px) {
    .parallax-bg {
        background-attachment: scroll;
        position: fixed;
        height: 120vh;
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
    }
}

.parallax-wrapper {
    position: relative;
    z-index: 1;
    background-color: transparent;
    min-height: 100vh;
}

/* 矢印リンク */
.arrow-link {
    display: inline-flex;
    align-items: center;
    color: #60a5fa; /* blue-400 */
    text-decoration: none;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.arrow-link:hover {
    color: #93c5fd; /* blue-300 */
}

.arrow-link:hover .arrow {
    transform: translateX(5px);
}

.arrow {
    position: relative;
    width: 30px;
    height: 1px;
    background-color: currentColor;
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.arrow::after {
    content: '';
    position: absolute;
    top: 1px;
    right: 0;
    transform-origin: right bottom;
    width: 10px;
    height: 1px;
    background-color: currentColor;
    transform: rotate(45deg);
}

/* パープル系の矢印リンク */
.arrow-link.purple {
    color: #c084fc; /* purple-400 */
}

.arrow-link.purple:hover {
    color: #d8b4fe; /* purple-300 */
}

/* アニメーション */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ゆっくりしたタイトルアニメーション */
@keyframes fadeInUpSlow {
    from {
        opacity: 0;
        transform: translateY(40px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* フェードインアニメーション */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 0.7;
        transform: translateY(0);
    }
}

.animate-fadeIn {
    animation: fadeIn 0.5s ease-out;
}

/* 背景画像ズームアニメーション */
@keyframes zoomOut {
    from {
        transform: scale(1.05);
    }

    to {
        transform: scale(1);
    }
}

/* reCAPTCHA v3 バッジを非表示 */
.grecaptcha-badge {
    visibility: hidden !important;
}

/* フォームフッターにreCAPTCHA表記を追加するためのスタイル */
.recaptcha-terms {
    text-align: center;
    font-size: 0.75rem;
    color: #9ca3af; /* text-gray-400 */
    margin-top: 1rem;
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
