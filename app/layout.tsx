import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>_Sans_JP } from "next/font/google";
import "./globals.css";
import Script from "next/script";
import RecaptchaProvider from "../components/RecaptchaProvider";

const montserrat = Montserrat({
  variable: "--font-montserrat",
  subsets: ["latin"],
  weight: ["400", "600"],
  display: 'swap',
  preload: true,
});

const notoSansJP = Noto_Sans_JP({
  variable: "--font-noto-sans-jp",
  subsets: ["latin", "latin-ext"],
  weight: ["400", "600", "700"],
  display: 'swap',
  preload: true,
});

export const metadata: Metadata = {
  title: "株式会社スペースカウボーイ | 見上げる空から、つながる宇宙へ",
  description: "宇宙事業部と映像・Web制作のクリエイティブ事業部で、宇宙から地上まで表現と技術で課題解決を行う福岡の企業です。",
  openGraph: {
    title: "株式会社スペースカウボーイ | 見上げる空から、つながる宇宙へ",
    description: "宇宙事業部と映像・Web制作のクリエイティブ事業部で、宇宙から地上まで表現と技術で課題解決を行う福岡の企業です。",
    type: "website",
    url: "https://www.space-cowboy.jp/?v=2",
    images: [
      {
        url: "https://www.space-cowboy.jp/img/ogp1200x630.png",
        width: 1200,
        height: 630,
        type: "image/png",
      },
    ],
    siteName: "株式会社スペースカウボーイ",
    locale: "ja_JP",
  },
  twitter: {
    card: "summary_large_image",
    site: "@cutkey5",
  },
  icons: {
    icon: [
      { url: "/img/favicon.ico" },
      { url: "/img/icon-32x32.png", sizes: "32x32", type: "image/png" },
    ],
    apple: [
      { url: "/img/apple-touch-icon-180x180.png", sizes: "180x180" },
    ],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ja">
      <head>
        {/* LCP画像のプリロード */}
        <link rel="preload" as="image" href="/img/universe.webp" />
        <link rel="preload" as="image" href="/img/service.webp" />
        <link rel="preload" as="image" href="/img/company_image.webp" />
        <link rel="preload" as="image" href="/img/contact_image.webp" />
        
        {/* Critical CSS - パララックス背景の即座表示 */}
        <style dangerouslySetInnerHTML={{
          __html: `
            .parallax-bg {
              background-image: url('/img/universe.webp');
              background-size: cover;
              background-position: center;
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100vh;
              z-index: -1;
              opacity: 0.7;
              will-change: transform;
            }
            .service-page .parallax-bg { background-image: url('/img/service.webp'); }
            .company-page .parallax-bg { background-image: url('/img/company_image.webp'); }
            .contact-page .parallax-bg { background-image: url('/img/contact_image.webp'); }
            .games-page .parallax-bg { background-image: url('/img/universe.webp'); }
            .articles-page .parallax-bg { background-image: url('/img/universe.webp'); }
            
            @media (min-width: 1025px) {
              .parallax-bg { background-attachment: fixed; transform: none !important; }
            }
            
            @supports (-webkit-touch-callout: none) {
              .parallax-bg { background-attachment: scroll; height: 120vh; }
            }
            
            @media only screen and (max-width: 1024px) {
              .parallax-bg {
                background-attachment: scroll;
                position: fixed;
                height: 120vh;
                transform: translateZ(0);
                -webkit-transform: translateZ(0);
                -webkit-backface-visibility: hidden;
                backface-visibility: hidden;
              }
            }
            
            .parallax-wrapper {
              position: relative;
              z-index: 1;
              background-color: transparent;
              min-height: 100vh;
            }
          `
        }} />
      </head>
      <body
        className={`${montserrat.variable} ${notoSansJP.variable} bg-gray-900 text-white antialiased`}
      >
        <RecaptchaProvider>
          {children}
        </RecaptchaProvider>
      </body>
      <Script
        async
        defer
        src="https://www.googletagmanager.com/gtag/js?id=G-HQFF46WQHF"
      />
      <Script id="google-analytics" defer>
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', 'G-HQFF46WQHF');
        `}
      </Script>
    </html>
  );
}
