import Link from 'next/link';
import Navigation from '../components/Navigation';
import Footer from '../components/Footer';
import CTASection from '../components/CTASection';
import Image from 'next/image';

export default function Home() {
  return (
    <main className="bg-gray-900/0 text-white">
      {/* Navigation Section */}
      <Navigation />
      
      {/* パララックス背景 */}
      <div className="parallax-bg" id="parallax-bg"></div>
      
      {/* 背景を暗くするオーバーレイ */}
      <div className="fixed inset-0 bg-black/20 z-0"></div>

      {/* Heroセクション本体 */}
      <div className="parallax-wrapper">
        <section
          className="min-h-screen flex flex-col justify-center items-start text-left px-6 pt-32 pb-16 md:px-12 md:pt-48 md:pb-24 relative z-10 bg-gray-900/0">
          <div className="max-w-4xl animate-fade-in">
            <h1 id="hero-title" className="text-6xl md:text-8xl text-white montserrat-bold mb-24"
              style={{ letterSpacing: '0.1em', animation: 'fadeInUpSlow 2s ease-out' }}>
              <div>
                <span>HELLO</span><br />
                <span>SPACEY-FUN</span>
              </div>
            </h1>

            <div className="max-w-2xl text-left leading-loose mb-12 text-white">
              <p id="hero-subtitle-1" className="text-2xl md:text-3xl font-medium text-white mb-8"
                style={{ animation: 'fadeIn 2s ease-out 1.5s both' }}>
                見上げよう空を、<br />
                つながろう宇宙で。
              </p>
              <p id="hero-subtitle-2" className="text-sm md:text-base font-light text-white leading-loose"
                style={{ animation: 'fadeIn 2s ease-out 1.5s both' }}>
                日々の忙しさのなか、ふと空を見上げて心を落ち着かせる。<br />
                そして夢を描き、未来へ思いを馳せる。<br />
                そんな瞬間で、人と人とをつなげていきたい。<br /><br />
                私たち自身も、宇宙という挑戦に本気で向き合っています。<br />
                だからこそ、挑戦するすべての人々に、心から寄り添います。<br />
                あなたの課題にも、SPACEYな視野でお応えします。<br /><br />
              </p>
            </div>

            <Link href="/contact" className="hero-cta-button inline-block">
              <p className="flex items-center justify-center">
                <span className="text-2xl mr-2">🚀</span>まずはご相談ください
              </p>
            </Link>

          </div>
        </section>
      </div>

      {/* Photo Section */}
      <div className="px-0 py-0 bg-black">
        <div className="max-w-full mx-auto grid grid-cols-1 md:grid-cols-2">
          <div>
            <Image src="/img/hero_image_01.webp" alt="Hero Image 1" width={1920} height={400} className="w-full h-[20vh] object-cover" />
          </div>
          <div>
            <Image src="/img/hero_image_02.webp" alt="Hero Image 2" width={1920} height={400} className="w-full h-[20vh] object-cover" />
          </div>
        </div>
      </div>

      {/* SERVICE Section */}
      <section id="service" className="bg-gray-900 text-white">
        <div className="max-w-6xl mx-auto px-6 pt-24 pb-24 md:px-12 md:pt-36 md:pb-24">
          <div className="flex justify-between items-center mb-16">
            <h2 className="text-4xl md:text-6xl montserrat-bold">SERVICE</h2>
            <Link href="/service" className="flex items-center text-blue-300 hover:text-blue-100 transition">
              <span className="text-sm md:text-base montserrat-regular">VIEW MORE &gt;</span>
            </Link>
          </div>
          <div className="mb-16 md:flex md:items-start md:gap-12">
            {/* 左側：テキスト */}
            <div className="md:w-1/2 grid grid-rows-[1fr_auto] gap-4">
              <h3 className="text-xl md:text-2xl mb-4">
                宇宙のことも<br />
                クリエイティブのことも。
              </h3>
              <p className="text-xs md:text-sm max-w-3xl mb-12 font-light text-gray-400 leading-loose">
                株式会社スペースカウボーイは、「宇宙事業部」と「クリエイティブ事業部」の2つの軸で事業を展開。
                宇宙産業におけるコンテンツ制作・情報発信・エンジニアリング・プロダクト構想から、
                地上のクリエイティブ課題まで幅広く支援しています。
              </p>

              <div>
                <Link href="/service" className="hero-cta-button inline-block">
                  <span className="flex items-center justify-center">
                    <span className="text-2xl mr-2">🌝</span>サービスの詳細
                  </span>
                </Link>
              </div>

            </div>
            {/* 右側：画像 */}
            <div className="mt-8 md:mt-0 md:w-1/2">
              <Image src="/img/article.webp" alt="サービス紹介画像"
                width={800} height={600}
                className="w-full h-auto" />
            </div>
          </div>

          {/* ライン */}
          <hr className="border-t border-gray-700 my-16" />

          {/* Divisions */}
          <div className="max-w-6xl mx-auto md:pb-12">
            {/* 宇宙事業部 */}
            <div className="mb-8">
              <div className="md:flex md:gap-12">
                {/* 左カラム */}
                <div className="md:w-3/5 mb-8 md:mb-0">
                  <div className="flex items-center mb-4">
                    <span className="text-3xl mr-3">🛰️</span>
                    <h4 className="text-xl md:text-2xl font-bold">宇宙事業部</h4>
                  </div>
                  <p className="text-blue-300 text-sm md:text-base mb-6 font-medium">宇宙と社会をつなぐ</p>
                  <p className="text-gray-300 text-sm leading-loose font-light">
                    宇宙に関する知識や人とのつながりを深めながら、
                    コンテンツ制作、情報発信、アプリ開発、プロダクト企画など、宇宙関連のあらゆる領域で幅広く支援。
                    民間・行政・大学をつなぐハブとしての機能を担います。
                  </p>
                  <Link href="/service#space-division" className="arrow-link">
                    <span className="text-sm">詳しく見る</span>
                    <span className="arrow"></span>
                  </Link>
                </div>

                {/* 右カラム */}
                <div className="md:w-2/5">
                  <div className="flex items-center mb-8">
                    <div className="w-16 border-t border-gray-500"></div>
                    <h4 className="text-sm font-semibold text-blue-200 ml-4 whitespace-nowrap">提供メニュー</h4>
                  </div>

                  <ul className="flex flex-wrap gap-2">
                    <li>
                      <span
                        className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                        広報戦略設計
                      </span>
                    </li>
                    <li>
                      <span
                        className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                        映像・動画制作
                      </span>
                    </li>
                    <li>
                      <span
                        className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                        セミナー・イベント企画
                      </span>
                    </li>
                    <li>
                      <span
                        className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                        Webアプリ開発
                      </span>
                    </li>
                    <li>
                      <span
                        className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                        衛星データ利活用
                      </span>
                    </li>
                    <li>
                      <span
                        className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                        宇宙コンテンツ企画
                      </span>
                    </li>
                  </ul>

                </div>

              </div>
            </div>

            {/* ライン */}
            <hr className="border-t border-gray-700 my-16" />

            {/* クリエイティブ戦略部 */}
            <div>
              <div className="md:flex md:gap-12">
                {/* 左カラム */}
                <div className="md:w-3/5 mb-8 md:mb-0">
                  <div className="flex items-center mb-4">
                    <span className="text-3xl mr-3">🎬</span>
                    <h3 className="text-xl md:text-2xl font-bold">クリエイティブ事業部</h3>
                  </div>
                  <p className="text-purple-300 text-sm md:text-base mb-6 font-medium">「伝える」をデザインする</p>
                  <p className="text-gray-300 text-sm leading-loose font-light">
                    宇宙に限らず、名刺・チラシ・映像・ロゴ・SNS・Webなど、
                    あらゆるクリエイティブ表現の課題を解決。企画・制作・運用まで一貫して伴走します。
                    映像制作を軸に培ってきた表現力と構成力に、
                    デザインの裏側まで理解し自ら動かせるエンジニアリングの柔軟性を加え、
                    一貫対応でクライアントの想いを形にします。
                  </p>
                  <Link href="/service#creative-division" className="arrow-link purple">
                    <span className="text-sm">詳しく見る</span>
                    <span className="arrow"></span>
                  </Link>
                </div>

                {/* 右カラム */}
                <div className="md:w-2/5">
                  <div className="flex items-center mb-8">
                    <div className="w-16 border-t border-gray-500"></div>
                    <h4 className="text-sm font-semibold text-purple-200 ml-4 whitespace-nowrap">提供メニュー</h4>
                  </div>

                  <div className="flex flex-wrap gap-3">
                    <span
                      className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                      クリエイティブトータル支援
                    </span>
                    <span
                      className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                      映像制作
                    </span>
                    <span
                      className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                      アニメーション制作
                    </span>
                    <span
                      className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                      Web/アプリ制作
                    </span>
                    <span
                      className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                      UI設計・LP制作
                    </span>
                    <span
                      className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                      SNS運用支援
                    </span>
                    <span
                      className="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                      プロモーション企画
                    </span>
                  </div>

                </div>

              </div>
            </div>
          </div>
          {/* ライン */}

        </div>
      </section>

      {/* Company Section */}
      <section id="company" className="bg-gray-800/70 text-white">
        <div className="max-w-6xl mx-auto px-6 pt-24 pb-24 md:px-12 md:pt-36 md:pb-24">
          <div className="flex justify-between items-center mb-16">
            <h2 className="text-4xl md:text-6xl montserrat-bold">COMPANY</h2>
            <Link href="/company" className="flex items-center text-blue-300 hover:text-blue-100 transition">
              <span className="text-sm md:text-base montserrat-regular">VIEW MORE &gt;</span>
            </Link>
          </div>
          <div className="mb-16 md:flex md:items-start md:gap-12">
            {/* 左側：テキスト */}
            <div className="md:w-1/2 grid grid-rows-[1fr_auto] gap-4">
              <h3 className="text-xl md:text-2xl mb-4">
                宇宙を身近に、<br />
                未来を創造する。
              </h3>
              <p className="text-xs md:text-sm max-w-3xl mb-12 font-light text-white leading-loose">
                株式会社スペースカウボーイは、2025年に設立された宇宙産業とクリエイティブを融合する企業です。
                最先端の技術と独創的な発想で、宇宙ビジネスの新たな地平を切り拓きます。
                私たちのビジョンは、誰もが宇宙の可能性に触れられる世界を実現することです。
              </p>

              <div>
                <Link href="/company" className="hero-cta-button inline-block">
                  <span className="flex items-center justify-center">
                    <span className="text-2xl mr-2">🏢</span>会社概要を見る
                  </span>
                </Link>
              </div>

            </div>
            {/* 右側：画像 */}
            <div className="mt-8 md:mt-0 md:w-1/2">
              <Image src="/img/company_overview.webp" alt="会社紹介画像"
                width={800} height={600}
                className="w-full h-auto" />
            </div>
          </div>
        </div>
      </section>

      <CTASection />

      <Footer />
    </main>
  );
}
