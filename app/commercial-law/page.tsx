import { Metadata } from 'next';
import Navigation from '../../components/Navigation';
import Footer from '../../components/Footer';

export const metadata: Metadata = {
  title: '特定商取引法に基づく表記 | 株式会社スペースカウボーイ',
  description: '株式会社スペースカウボーイの特定商取引法に基づく表記です。事業者情報、販売価格、返品・返金等についてご確認ください。',
  openGraph: {
    title: '特定商取引法に基づく表記 | 株式会社スペースカウボーイ',
    description: '株式会社スペースカウボーイの特定商取引法に基づく表記です。事業者情報、販売価格、返品・返金等についてご確認ください。',
    url: 'https://www.space-cowboy.jp/commercial-law',
  },
};

export default function CommercialLawPage() {
  return (
    <main className="bg-gray-900/50 text-white tokushoho-page">
      <Navigation />
      
      {/* パララックス背景 */}
      <div className="parallax-bg" id="parallax-bg"></div>

      {/* メインコンテンツ */}
      <div className="parallax-wrapper">
        <section className="min-h-screen pt-32 md:pt-48 relative z-10">
          <div className="container mx-auto px-4 py-12 max-w-4xl">
            <div className="bg-gray-900/80 backdrop-blur-sm rounded-lg p-8 border border-gray-700">
              <h1 className="pt-serif text-xl font-bold text-indigo-300 mb-8 text-center">
                特定商取引法に基づく表記
              </h1>

              <div className="space-y-6">
                <div>
                  <h2 className="text-sm font-semibold text-indigo-300 mb-2">事業者名</h2>
                  <p className="text-xs text-gray-300">株式会社スペースカウボーイ</p>
                </div>

                <div>
                  <h2 className="text-sm font-semibold text-indigo-300 mb-2">販売責任者</h2>
                  <p className="text-xs text-gray-300">香月 浩一</p>
                </div>

                <div>
                  <h2 className="text-sm font-semibold text-indigo-300 mb-2">所在地</h2>
                  <p className="text-xs text-gray-300">〒810-0001 福岡県福岡市中央区天神4-6-28 天神ファーストビル7階</p>
                </div>

                <div>
                  <h2 className="text-sm font-semibold text-indigo-300 mb-2">メールアドレス</h2>
                  <p className="text-xs text-gray-300"><EMAIL></p>
                </div>

                <div>
                  <h2 className="text-sm font-semibold text-indigo-300 mb-2">電話番号</h2>
                  <p className="text-xs text-gray-300">080-8953-6893</p>
                  <p className="text-gray-400 text-xs">受付時間：平日10:00〜18:00（土日祝を除く）</p>
                </div>

                <div>
                  <h2 className="text-sm font-semibold text-indigo-300 mb-2">販売価格</h2>
                  <p className="text-xs text-gray-300">500円（税込）または USD 3〜5</p>
                </div>

                <div>
                  <h2 className="text-sm font-semibold text-indigo-300 mb-2">商品・サービスの内容</h2>
                  <p className="text-xs text-gray-300">
                    本商品は、宇宙と星座を活用したデジタルエンタメ体験を支援する目的のデジタルコンテンツです。<br />
                    物理的な商品の発送はございません。
                  </p>
                </div>

                <div>
                  <h2 className="text-sm font-semibold text-indigo-300 mb-2">引渡し時期</h2>
                  <p className="text-xs text-gray-300">購入後、即時Web画面またはメールにて案内いたします</p>
                </div>

                <div>
                  <h2 className="text-sm font-semibold text-indigo-300 mb-2">返品・返金について</h2>
                  <p className="text-xs text-gray-300">
                    本商品は支援という性質上、返品・返金には応じられません。<br />
                    予めご了承ください。
                  </p>
                </div>

                <div>
                  <h2 className="text-sm font-semibold text-indigo-300 mb-2">お支払い方法</h2>
                  <p className="text-xs text-gray-300">Stripe決済（クレジットカード、デビットカード等）</p>
                </div>

                <div>
                  <h2 className="text-sm font-semibold text-indigo-300 mb-2">追加手数料等の追加料金</h2>
                  <p className="text-xs text-gray-300">追加料金は発生しません。</p>
                </div>

                <div>
                  <h2 className="text-sm font-semibold text-indigo-300 mb-2">決済期間</h2>
                  <p className="text-xs text-gray-300">クレジットカード決済は即時処理されます。</p>
                </div>
              </div>

            </div>
          </div>
        </section>
      </div>

      <Footer />
    </main>
  );
}