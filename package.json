{"name": "spacecowboy-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "eslint"}, "dependencies": {"@mdx-js/loader": "^3.1.1", "@mdx-js/react": "^3.1.1", "@next/mdx": "^15.5.3", "@types/diff": "^8.0.0", "@types/mdx": "^2.0.13", "diff": "^8.0.2", "gray-matter": "^4.0.3", "next": "15.5.3", "next-mdx-remote": "^5.0.0", "react": "19.1.0", "react-dom": "19.1.0", "react-google-recaptcha-v3": "^1.11.0", "rehype-highlight": "^7.0.2", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1", "remark-mdx-frontmatter": "^5.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "tailwindcss": "^4", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.2"}}