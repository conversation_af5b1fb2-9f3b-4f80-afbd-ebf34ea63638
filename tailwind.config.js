/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./content/**/*.{md,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'montserrat': ['var(--font-montserrat)', 'sans-serif'],
        'noto-sans-jp': ['var(--font-noto-sans-jp)', 'sans-serif'],
      },
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        'cosmic-blue': '#2ea7e0',
        'cosmic-teal': '#00a8b5',
        'cosmic-black': '#0a0a0a',
        'cosmic-dark': '#1a1a2e',
      },
      animation: {
        'fadeIn': 'fadeIn 0.5s ease-out',
        'fadeInUp': 'fadeInUp 0.6s ease-out',
        'fadeInUpSlow': 'fadeInUpSlow 0.8s ease-out',
        'zoomOut': 'zoomOut 5s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        fadeInUp: {
          '0%': { opacity: '0', transform: 'translateY(30px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        fadeInUpSlow: {
          '0%': { opacity: '0', transform: 'translateY(40px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        zoomOut: {
          '0%': { transform: 'scale(1.05)' },
          '100%': { transform: 'scale(1)' },
        },
      },
      aspectRatio: {
        'video': '16 / 9',
      },
    },
  },
  plugins: [],
  // CSS分割とパフォーマンス最適化
  corePlugins: {
    // 未使用のプラグインを無効化してバンドルサイズを削減
    container: false,
  },
  // development時のJITモードでファイルサイズ削減
  mode: 'jit',
  // CSS purging設定（本番環境のみ）
  purge: {
    enabled: process.env.NODE_ENV === 'production',
    content: [
      "./app/**/*.{js,ts,jsx,tsx,mdx}",
      "./pages/**/*.{js,ts,jsx,tsx,mdx}",
      "./components/**/*.{js,ts,jsx,tsx,mdx}",
      "./content/**/*.{md,mdx}",
    ],
    // 動的に生成されるクラス名を保護
    safelist: [
      'line-clamp-1',
      'line-clamp-2', 
      'line-clamp-3',
      'hero-cta-button',
      'concept-cta-button',
      'parallax-bg',
      'parallax-wrapper',
      'arrow-link',
      'cosmic-theme',
      'cosmic-background',
      'wing-card',
      'wing-inner',
      'card-front',
      'card-back',
      'cosmic-btn-primary',
      'cosmic-btn-secondary',
      'cosmic-title',
      // 動的クラス名のパターンマッチ
      { pattern: /^(result-|wing-|card-|cosmic-)/ },
      { pattern: /^(fade-|animate-)/ },
    ],
    // CSS変数は保持
    options: {
      keyframes: true,
      fontFace: true,
    },
  },
}