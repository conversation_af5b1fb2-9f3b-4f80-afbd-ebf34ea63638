name: Deploy Next.js static to Xserver

on:
  push:
    branches: [ main ]
    paths:
      - 'app/**'
      - 'components/**'
      - 'content/**'
      - 'lib/**'
      - 'utils/**'
      - 'public/**'
      - 'package.json'
      - 'next.config.*'
      - 'tailwind.config.*'
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: './package-lock.json'

      - name: Install dependencies
        run: npm ci

      - name: Build Next.js application (static export)
        run: npm run build
        env:
          NEXT_PUBLIC_DEV_MODE: false
          NEXT_PUBLIC_CONTACT_GAS_ENDPOINT: ${{ secrets.NEXT_PUBLIC_CONTACT_GAS_ENDPOINT }}
          NEXT_PUBLIC_PASSWORD: ${{ secrets.NEXT_PUBLIC_PASSWORD }}
          NEXT_PUBLIC_ESUPPORT_GAS_ENDPOINT: ${{ secrets.NEXT_PUBLIC_ESUPPORT_GAS_ENDPOINT }}
          NEXT_PUBLIC_CLOUD_RUN_API: ${{ secrets.NEXT_PUBLIC_CLOUD_RUN_API }}
          NEXT_PUBLIC_GA_ID: ${{ secrets.NEXT_PUBLIC_GA_ID }}
          NEXT_PUBLIC_RECAPTCHA_ENABLED: ${{ secrets.NEXT_PUBLIC_RECAPTCHA_ENABLED }}
          NEXT_PUBLIC_RECAPTCHA_SITE_KEY: ${{ secrets.NEXT_PUBLIC_RECAPTCHA_SITE_KEY }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          GOOGLE_SPREADSHEET_ID: ${{ secrets.GOOGLE_SPREADSHEET_ID }}
          GOOGLE_SHEET_NAME: ${{ secrets.GOOGLE_SHEET_NAME }}
          GOOGLE_CLIENT_JSON: ${{ secrets.GOOGLE_CLIENT_JSON }}

      - name: Deploy all files to Xserver
        uses: SamKirkland/FTP-Deploy-Action@v4.3.4
        with:
          server: ${{ secrets.FTP_SERVER }}
          username: ${{ secrets.FTP_USERNAME }}
          password: ${{ secrets.FTP_PASSWORD }}
          protocol: ftps         # XserverはFTPS推奨
          port: 21
          timeout: 600000        # 10分に延長（600秒 = 600,000ms）
          local-dir: ./out/      # ← 重要: 静的ファイルのみ
          server-dir: ./                          # ← public_html直下（ルート）に配置
          dangerous-clean-slate: true             # ← 既存の .tsx などソースを掃除
          exclude: |
            **/.git*
            **/.github/**
            **/node_modules/**
            **/.vscode/**
            **/*.log
            **/.DS_Store