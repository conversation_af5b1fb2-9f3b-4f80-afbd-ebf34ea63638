# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes

# 一時ファイル
*.tmp
*.temp
*.log

# アーカイブされた開発資料
/archive/wip/analyzer_env/
/archive/wip/json/
/archive/wip/img/

# Python仮想環境（API用）
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEの設定
.vscode/
!.vscode/settings.json
.idea/
*.swp
*.swo
*~